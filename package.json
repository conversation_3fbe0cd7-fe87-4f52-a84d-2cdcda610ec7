{"dependencies": {"@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@aws-sdk/lib-storage": "^3.717.0", "@aws-sdk/util-dynamodb": "^3.716.0", "@googlemaps/polyline-codec": "^1.0.28", "@remix-run/express": "^2.13.1", "aws-sdk": "^2.1692.0", "axios": "^1.7.7", "bottleneck": "^2.19.5", "cls-hooked": "^4.2.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "express": "^4.21.2", "firebase-admin": "^13.4.0", "mime-types": "^2.1.35", "nanoid": "^5.0.9", "speakeasy": "^2.0.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "name": "farmersmandi-business", "version": "0.0.1", "description": "Service for business", "main": "src/index.js", "type": "module", "scripts": {"build": "npm run build:server && npm run build:client", "build:server": "tsc && tsc-alias", "build:client": "cd apps/business-console && npm install && npx remix vite:build", "start": "node dist/index.js", "eb-start": "npm install && npm run build && npm start", "postinstall": "npm run build", "dev": "tsx watch src/index.ts", "migrate": "tsx src/migrations/index.ts", "migrate:whatsapp-status": "tsx src/migrations/002-add-whatsapp-status-tracking.ts up", "migrate:validate-whatsapp-status": "tsx src/migrations/002-add-whatsapp-status-tracking.ts validate", "migrate:rollback-whatsapp-status": "tsx src/migrations/002-add-whatsapp-status-tracking.ts down", "migrate:notification-log": "tsx src/migrations/notification-log-migration.ts", "migrate:notification-log-backup": "tsx src/migrations/notification-log-migration.ts backup", "migrate:notification-log-validate": "tsx src/migrations/notification-log-migration.ts validate", "migrate:notification-log-rollback": "tsx src/migrations/notification-log-migration.ts rollback", "test:webhook": "tsx src/tests/index.ts", "test:health": "tsx src/tests/index.ts health", "test:notification-system": "tsx src/tests/simple-test-demo.ts"}, "author": "", "license": "ISC", "devDependencies": {"@types/cls-hooked": "^4.3.9", "@types/date-fns": "^2.5.3", "@types/express": "^4.17.21", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.21", "@types/node-fetch": "^2.6.12", "@types/speakeasy": "^2.0.10", "concurrently": "^9.1.0", "exceljs": "^4.4.0", "nodemon": "^3.1.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "engines": {"node": ">=16"}}