export interface SellerItem {
  Id: number;
  sellerId: number;
  masterItemId: number;
  unit: string;
  weightFactor: number;
  packaging: string;
  name: string;
  picture: string;
  minOrderQty: number;
  incrementOrderQty: number;
  maxOrderQty: number;
  maxAvailableQty: number;
  pricePerUnit: number;
  isActive: boolean;
  rating: number;
  nameInKannada: string;
  nameInTelugu: string;
  nameInTamil: string;
  nameInMalayalam: string;
  nameInHindi: string;
  nameInAssame: string;
  nameInGujarati: string;
  nameInMarathi: string;
  nameInBangla: string;
  displayOrder: number,
  disabled: boolean,
  description: string,
  ignoreDiscounts: boolean,
  strikeOffPrice: number,
  taxExempt: boolean,
  freeItem: boolean,
  supplierItemId: number,
  supplierName: string,
  distChargePu: number,
  distChargePc: number,
  maxAcPkg: number,
  minAcPkg: number,
  maxDistcPkg: number,
  minDistcPkg: number,
  diet?: string;
  ratingCount?: number;
  packagingCharge?: number;
}

export interface SelectedSellerItem {
  deliveryDate: string;
  bookedQty: number;
  deliveredQty: number;
  cancelledQty: number;
  returnedQty: number;
  revenue: number;
}

export interface AddOnItem {
  id: number;
  sId: string;
  name: string;
  seq: number;
  price: number;
  qty: number;
  diet: string;
}
export interface AddOnGroup {
  id?: number;
  sId: string;
  minSelect: number;
  maxSelect: number;
  name: string;
  description: string;
  seq: number;
  varient: boolean;
  addOnItemList?: AddOnItem[];
}
export interface SelectedItem {
  id?: number;
  name: string;
  groupName: string;
  seq: number;
  price: number;
  strikeoffPrice: number;
  qty: number;
  sId: string;
  addOnGroupList: AddOnGroup[];
}




