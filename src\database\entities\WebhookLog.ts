import { WebhookStatus, MessageType, MessageDirection, WhatsAppMessageStatus } from '../../types/webhook.types.js';
import { WhatsAppError } from '../../types/whatsapp-webhook.types.js';
import { KeyType, ScalarAttributeType, BillingMode, ProjectionType } from '@aws-sdk/client-dynamodb';
import { TimeUtil } from '../../utils/time.util.js';

const nodeEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
const webhookLogsTableName = `webhook_logs_${nodeEnv}`;

export interface WebhookLog {
    // Primary keys
    webhookId: string;                    // Partition key (generated)
    timestamp: number;                    // Sort key (when received - milliseconds timestamp)
    timestampISO: string;                 // ISO timestamp in IST timezone for readability
    
    // Identifiers for filtering and analytics
    businessNumber: string;               // GSI1 partition key
    customerNumber?: string;              // GSI2 partition key
    messageId?: string;                   // WhatsApp message ID (for messages and status updates)
    phoneNumberId?: string;               // WhatsApp phone number ID
    
    // Message classification
    messageType: MessageType;             // GSI3 partition key
    messageDirection: MessageDirection;   // Always INBOUND for webhooks
    status: WebhookStatus;                // GSI4 partition key
    messageCode?: MessageCode;            // GSI5 partition key - for searching specific message types
    
    // WhatsApp specific status and error tracking
    whatsappStatus?: WhatsAppMessageStatus; // GSI6 partition key - Message delivery status (sent, delivered, read, failed)
    whatsappErrors?: WhatsAppError[];    // WhatsApp error details from webhook
    
    // Raw data (complete webhook payload)
    rawPayload: Record<string, any>;
    
    // Extracted message content
    messageContent?: string;              // Text content or summary
    contactName?: string;                 // Contact profile name
    location?: {
        latitude?: string;
        longitude?: string;
        address?: string;
        name?: string;
    };
    
    // Interactive message data
    interactiveData?: {
        type?: string;                    // button_reply, list_reply
        buttonId?: string;
        buttonTitle?: string;
        payload?: string;
    };
    
    // Processing metadata with timestamps
    receivedAt: number;                   // When webhook was received (milliseconds timestamp)
    receivedAtISO: string;                // When webhook was received (IST ISO timestamp)
    processedAt?: number;                 // When processing completed (milliseconds timestamp)
    processedAtISO?: string;              // When processing completed (IST ISO timestamp)
    processingDuration?: number;          // Processing time in milliseconds
    
    // Error handling
    errorMessage?: string;
    errorDetails?: Record<string, any>;
    retryCount?: number;
    
    // Metadata
    lastUpdated: number;                  // milliseconds timestamp
    lastUpdatedISO: string;               // IST ISO timestamp
}

/**
 * Message codes for categorizing specific webhook types for easy searching
 */
export enum MessageCode {
    GREETING = 'GREETING',              // Greeting messages and say_hello interactions
    ORDER_STATUS = 'ORDER_STATUS',      // Order status inquiries
    FREE_GIFT_CLAIM = 'FREE_GIFT_CLAIM' // Free gift claim requests
}

/**
 * DynamoDB table configuration for webhook logs
 */
export const webhookLogTableConfig = {
    TableName: webhookLogsTableName,
    BillingMode: BillingMode.PAY_PER_REQUEST,
    AttributeDefinitions: [
        {
            AttributeName: 'webhookId',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'timestamp',
            AttributeType: ScalarAttributeType.N
        },
        {
            AttributeName: 'businessNumber',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'customerNumber',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'messageType',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'status',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'messageCode',
            AttributeType: ScalarAttributeType.S
        },
        {
            AttributeName: 'whatsappStatus',
            AttributeType: ScalarAttributeType.S
        }
    ],
    KeySchema: [
        {
            AttributeName: 'webhookId',
            KeyType: KeyType.HASH
        },
        {
            AttributeName: 'timestamp',
            KeyType: KeyType.RANGE
        }
    ],
    GlobalSecondaryIndexes: [
        {
            IndexName: 'BusinessNumberIndex',
            KeySchema: [
                {
                    AttributeName: 'businessNumber',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'CustomerNumberIndex',
            KeySchema: [
                {
                    AttributeName: 'customerNumber',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'MessageTypeIndex',
            KeySchema: [
                {
                    AttributeName: 'messageType',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'StatusIndex',
            KeySchema: [
                {
                    AttributeName: 'status',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'MessageCodeIndex',
            KeySchema: [
                {
                    AttributeName: 'messageCode',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'WhatsAppStatusIndex',
            KeySchema: [
                {
                    AttributeName: 'whatsappStatus',
                    KeyType: KeyType.HASH
                },
                {
                    AttributeName: 'timestamp',
                    KeyType: KeyType.RANGE
                }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        }
    ]
};

export default webhookLogTableConfig; 