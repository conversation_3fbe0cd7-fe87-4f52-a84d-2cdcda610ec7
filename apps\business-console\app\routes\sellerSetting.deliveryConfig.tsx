
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Trash2, Plus, DollarSign, ShoppingCart, Truck } from "lucide-react"
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useRevalidator } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { LoaderFunction } from "@remix-run/node"
import { useToast } from "~/components/ui/ToastProvider";
import { getDeliveryConfigs, createDcConfig, deleteDeliveryConfig } from "~/services/deliveryConfigService"
import { dclistingResponse, DcBody, ConfigType } from "~/types/api/businessConsoleService/DeliveryConfig"

interface Loaderdata {
  data: dclistingResponse[];
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const sellerId = user?.userDetails?.sellerId;
  try {
    if (!sellerId) {
      throw new Response("Seller ID is required", { status: 400 });
    }
    const response = await getDeliveryConfigs(sellerId, request);
    return withResponse({ data: response.data?.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId = user?.userDetails?.sellerId;

  if (!sellerId) {
    return json(
      { data: null, actionType: actionType, success: false, error: "Seller ID is required" },
      { status: 400 }
    );
  }

  if (actionType === "deleteDeliveryConfig") {
    const configId = Number(formData.get("configId"));
    try {
      const response = await deleteDeliveryConfig(configId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "addDeliveryConfig") {
    const slabChangeAt = Number(formData.get("slabChangeAt"));
    const lowerSlabCapAt = Number(formData.get("lowerSlabCapAt"));
    const higherSlabCapAt = Number(formData.get("higherSlabCapAt"));

    // Create two payloads
    const payload1: Partial<DcBody> = {
      sellerId: sellerId,
      configType: ConfigType.ORDER_VALUE_BASED,
      minOrderValue: 0,
      maxOrderValue: slabChangeAt,
      maxBuyerDeliveryCharge: lowerSlabCapAt,
      // maxSellerDeliveryCharge: 0,
      buyerPercentage: 100,
      sellerPercentage: 0,
      active: true
    };

    const payload2: Partial<DcBody> = {
      sellerId: sellerId,
      configType: ConfigType.ORDER_VALUE_BASED,
      minOrderValue: slabChangeAt + 1,
      maxOrderValue: 10000,
      maxBuyerDeliveryCharge: higherSlabCapAt,
      buyerPercentage: 100,
      sellerPercentage: 0,
      active: true
    };

    try {
      // Create first configuration
      await createDcConfig(payload1, request);
      // Create second configuration
      await createDcConfig(payload2, request);

      return json(
        { data: null, actionType: actionType, success: true },
        { status: 200 }
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});


export default function DeliveryConfig() {
  const { data: deliveryConfigs } = useLoaderData<Loaderdata>();
  const { revalidate } = useRevalidator();
  const [showForm, setShowForm] = useState(false);
  const [slabChangeAt, setSlabChangeAt] = useState(400);
  const [lowerSlabCapAt, setLowerSlabCapAt] = useState(60);
  const [higherSlabCapAt, setHigherSlabCapAt] = useState(30);

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();

  const { showToast } = useToast();

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Delivery Configuration deleted successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Failed to delete Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Delivery Configuration created successfully", "success");
        setShowForm(false);
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Failed to create Delivery Configuration", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  const handleDelete = (configId: number) => {
    if (confirm("Are you sure you want to delete this delivery configuration?")) {
      const formData = new FormData();
      formData.append("actionType", "deleteDeliveryConfig");
      formData.append("configId", configId.toString());
      fetcher.submit(formData, { method: "POST" });
    }
  };

  const handleAddConfig = () => {
    const formData = new FormData();
    formData.append("actionType", "addDeliveryConfig");
    formData.append("slabChangeAt", slabChangeAt.toString());
    formData.append("lowerSlabCapAt", lowerSlabCapAt.toString());
    formData.append("higherSlabCapAt", higherSlabCapAt.toString());
    fetcher.submit(formData, { method: "POST" });
  };

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto mb-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl md:text-3xl font-bold text-gray-900">Delivery Charge Configurations</h1>
              <p className="mt-2 text-gray-600">Manage your delivery charge configurations</p>
            </div>
          </div>
        </div>

        {/* Content */}
        {deliveryConfigs && deliveryConfigs.length > 0 ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {deliveryConfigs.map((config) => (
              <Card key={config.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Config #{config.id}</CardTitle>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(config.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Order Range:</span>
                    <span className="text-sm">₹{config.minOrderValue} - ₹{config.maxOrderValue}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Max Buyer Charge:</span>
                    <span className="text-sm">₹{config.maxBuyerDeliveryCharge}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Buyer :</span>
                    <span className="text-sm">{config.buyerPercentage}%</span>
                    <span className="text-sm font-medium ml-2">Seller :</span>
                    <span className="text-sm">{config.sellerPercentage}%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                      }`}>
                      {config.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div>
                <Truck className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              </div>

              {!showForm ? (
                <>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No Delivery Charge Configurations Found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Set up your delivery charge configuration to manage how delivery costs are split between you and your customers.
                  </p>

                  <Button
                    onClick={() => setShowForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Delivery Configuration
                  </Button>
                </>
              ) : (
                <Card className="text-left">
                  <CardHeader>
                    <CardTitle className="text-lg">Configure Delivery Charges</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="slabChangeAt" className="text-sm font-medium">
                        Order Value Threshold (₹)
                      </Label>
                      <Input
                        id="slabChangeAt"
                        type="number"
                        value={slabChangeAt}
                        onChange={(e) => setSlabChangeAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="400"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Orders above this amount will have lower delivery charges
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="lowerSlabCapAt" className="text-sm font-medium">
                        Max Delivery Charge for Orders Below Threshold (₹)
                      </Label>
                      <Input
                        id="lowerSlabCapAt"
                        type="number"
                        value={lowerSlabCapAt}
                        onChange={(e) => setLowerSlabCapAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="60"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum amount customers pay for orders under ₹{slabChangeAt}
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="higherSlabCapAt" className="text-sm font-medium">
                        Max Delivery Charge for Orders Above Threshold (₹)
                      </Label>
                      <Input
                        id="higherSlabCapAt"
                        type="number"
                        value={higherSlabCapAt}
                        onChange={(e) => setHigherSlabCapAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="30"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum amount customers pay for orders above ₹{slabChangeAt}
                      </p>
                    </div>

                    <div className="bg-blue-50 p-3 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-900 mb-2">How it works:</h4>
                      <ul className="text-xs text-blue-800 space-y-1">
                        <li>• Orders under ₹{slabChangeAt}: Customer pays max ₹{lowerSlabCapAt}, you cover the rest</li>
                        <li>• Orders above ₹{slabChangeAt}: Customer pays max ₹{higherSlabCapAt}, you cover the rest</li>
                        <li>• This encourages larger orders while keeping delivery affordable</li>
                      </ul>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={handleAddConfig}
                        disabled={fetcher.state === "submitting" || fetcher.state === "loading"}
                        className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
                      >
                        {fetcher.state === "submitting" ? "Saving..." : "Save Configuration"}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowForm(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
