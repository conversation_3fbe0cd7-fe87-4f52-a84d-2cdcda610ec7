import { use<PERSON><PERSON>cher } from "@remix-run/react";
import React, { useState, useEffect, useRef } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { Suppliers } from "~/types/api/businessConsoleService/SellerManagement";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { SellerItem } from "~/types/api/businessConsoleService/MyItemList";

interface EditModalProps {
  isOpen: boolean;
  data: Partial<SellerItem>;
  onClose: () => void;
  multiple: boolean;
  supplierList: Suppliers[];
  onSave: (updatedData: any) => void;
}

const fields = [
  { name: "name", type: "text", label: "Name" },
  { name: "unit", type: "text", label: "Unit" },
  { name: "packaging", type: "text", label: "Packaging" },
  { name: "weightFactor", type: "number", label: "Weight Factor" },
  { name: "maxAvailableQty", type: "number", label: "Max Available Quantity" },
  { name: "minOrderQty", type: "number", label: "Min Order Quantity" },
  { name: "maxOrderQty", type: "number", label: "Max Order Quantity" },
  { name: "incrementOrderQty", type: "number", label: "Increment Order Quantity" },
  { name: "pricePerUnit", type: "number", label: "Price per Unit" },
  { name: "displayOrder", type: "number", label: "Display Order" },
  { name: "description", type: "text", label: "Description" },
  { name: "strikeOffPrice", type: "number", label: "StrikeOffPrice" },
  { name: "distChargePu", type: "number", label: "DistChargePu" },
  { name: "distChargePc", type: "number", label: "DistChargePc" },
  { name: "minAcPkg", type: "number", label: "MinAcPkg" },
  { name: "maxAcPkg", type: "number", label: "MaxAcPkg" },
  { name: "minDistcPkg", type: "number", label: "MinDistcPkg" },
  { name: "maxDistcPkg", type: "number", label: "MaxDistcPkg" },
  { name: "packagingCharge", type: "number", label: "Packaging Charge" },
];

const EditModal: React.FC<EditModalProps> = ({
  isOpen,
  data,
  onClose,
  onSave,
  multiple,
  supplierList,
}) => {
  const [formData, setFormData] = useState<Partial<SellerItem>>(data);
  const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setFormData(data);
  }, [data]);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setUploadError(null);

    const MAX_FILE_SIZE = 500 * 1024; // 500kb
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];

    const validFile = files.find((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });

    if (!validFile) return;

    const uploadFormData = new FormData();
    uploadFormData.append("_action", "uploadImage");
    uploadFormData.append("file", validFile, validFile.name);

    setUploading(true);

    uploadFetcher.submit(uploadFormData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data",
    });
  };

  // Only update the changed field, and handle number fields properly
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]:
        type === "number"
          ? value === "" ? "" : Number(value)
          : type === "radio" && (name === "taxExempt" || name === "freeItem")
            ? value === "true"
            : value,
    }));
  };

  useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrl = uploadFetcher.data.fileUrl;
        setFormData((prev: any) => ({
          ...prev,
          picture: uploadedUrl,
        }));
        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
      setUploading(false);
    }
  }, [uploadFetcher.data]);

  const handleSave = () => {
    // if selectedItem is present then compare with changed formData and remove unchanged fields
    const updatedData: Partial<SellerItem> = { ...formData };
    if (Object.keys(data).length > 0) {
      for (const key in formData) {
        const typedKey = key as keyof SellerItem;
        if (updatedData[typedKey] === data[typedKey]) {
          delete updatedData[typedKey];
        }
      }
      updatedData.Id = data.Id;
    }

    onSave(updatedData);
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="p-4 bg-white rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl max-h-[80vh] flex flex-col"
        style={{ width: "95vw" }}
      >
        <DialogTitle className="text-xl font-bold mb-2 text-center">Edit Seller Item</DialogTitle>
        <div className="flex-1 overflow-y-auto space-y-4">
          {/* Image Upload */}
          <div className="flex flex-col items-center gap-2">
            <h3 className="text-base font-semibold text-gray-800">Upload Image</h3>
            <div className="flex items-center gap-4">
              {formData && 'picture' in formData && formData.picture && (
                <img
                  src={formData.picture}
                  alt="Preview"
                  className="h-20 w-20 object-cover rounded-full border-2 border-gray-300"
                />
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="file:mr-4 file:py-1 file:px-3 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
            {uploadError && <p className="text-red-500 text-xs">{uploadError}</p>}
          </div>

          {/* Editable Fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {fields.map((field) => (
              <div key={field.name} className="flex flex-col">
                <label className="text-xs font-medium text-gray-700 mb-1">
                  {field.label}
                </label>
                <input
                  type={field.type}
                  name={field.name}
                  value={
                    field.type === "number"
                      ? (formData as any)?.[field.name] ?? ""
                      : (formData as any)?.[field.name] || ""
                  }
                  onChange={handleChange}
                  className="p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm"
                  placeholder={field.label}
                  min={field.type === "number" ? 0 : undefined}
                />
              </div>
            ))}
          </div>

          {/* Supplier Select */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Select Supplier
            </label>
            <select
              name="supplierItemId"
              value={formData.supplierItemId}
              onChange={handleChange}
              required
              className="w-full rounded border border-gray-300 p-2 focus:border-blue-400 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm"
            >
              <option value="">Select Supplier</option>
              {supplierList?.map((supplier) => (
                <option
                  key={supplier?.supplierItemId}
                  value={supplier?.supplierItemId?.toString()}
                >
                  {supplier?.supplierName}
                </option>
              ))}
            </select>
          </div>

          {/* Radio Groups */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="flex flex-col">
              <label className="text-xs font-medium text-gray-700 mb-1">Exclude Tax:</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-1 text-xs">
                  <input
                    type="radio"
                    name="taxExempt"
                    value="true"
                    checked={formData?.taxExempt === true}
                    onChange={handleChange}
                  />
                  Yes
                </label>
                <label className="flex items-center gap-1 text-xs">
                  <input
                    type="radio"
                    name="taxExempt"
                    value="false"
                    checked={formData?.taxExempt === false}
                    onChange={handleChange}
                  />
                  No
                </label>
              </div>
            </div>
            <div className="flex flex-col">
              <label className="text-xs font-medium text-gray-700 mb-1">Free Item:</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-1 text-xs">
                  <input
                    type="radio"
                    name="freeItem"
                    value="true"
                    checked={formData?.freeItem === true}
                    onChange={handleChange}
                  />
                  Yes
                </label>
                <label className="flex items-center gap-1 text-xs">
                  <input
                    type="radio"
                    name="freeItem"
                    value="false"
                    checked={formData?.freeItem === false}
                    onChange={handleChange}
                  />
                  No
                </label>
              </div>
            </div>
          </div>

          {/* Diet Select */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-1">Diet:</label>
            <Select
              value={formData?.diet}
              onValueChange={(value) =>
                setFormData((prev: any) => ({ ...prev, diet: value }))
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select Diet" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="veg">Veg</SelectItem>
                <SelectItem value="nonveg">Nonveg</SelectItem>
                <SelectItem value="egg">Egg</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="w-full sm:w-auto px-5 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="w-full sm:w-auto px-5 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
          >
            Save
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditModal;