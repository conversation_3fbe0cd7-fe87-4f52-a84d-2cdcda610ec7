import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Input } from "../components/ui/input"
import { json, ActionFunction, LoaderFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useSearchParams } from "@remix-run/react"
import { withAuth, withResponse } from "../utils/auth-utils"
import { useToast } from "../hooks/use-toast"
import { DateRange } from "react-day-picker"
import SpinnerLoader from "../components/loader/SpinnerLoader"
// Customer Analysis Components
import ConversionRate from "../components/common/ConversionRate";
import CustomersAcquisition from "../components/common/CustomerAcquisation";
import CustomerSales from "../components/common/CustomerSales";
import { getAcquiSitionRates, getConversionRate, getSellerSales } from "../services/buyerSetting";
import { getBuyerSummary, updateFbDiscountPrice } from "../services/businessConsoleService";
import { CustomerAcquisition, CustomerConversionRate, SellerSales } from "../types/api/businessConsoleService/BuyerAccountingResponse";
import { BuyerSummaryDetailsResponseItem } from "../types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import {
  Users,
  Star,
  Heart,
  TrendingUp,
  TrendingDown,
  Phone,
  MapPin,
  AlertTriangle,
  X,
  Info,
  Pencil,
  Save,
  ChevronLeft,
  ChevronRight,
  User,
  ArrowUp,
  ArrowDown,
  Gift
} from "lucide-react";
import { LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

type ActionIntent = "UpdateFbDiscount";

export interface BuyerDetails {
  buyerId: number;
  buyerName: string;
  ownerName: string;
  address: string;
  mobileNumber: string;
  totalOrders: number;
  totalAmount: number;
  pendingAmount: number;
  lastOrderedDate: string;
}

interface LoaderData {
  sales: SellerSales | null,
  customerConversionRates: CustomerConversionRate | null,
  customerAcquisitionRate: CustomerAcquisition | null,
  week: number,
  // Customer List Data
  customers: BuyerSummaryDetailsResponseItem[];
  currentPage: number;
  hasNextPage: boolean;
  hasMoreData: boolean;
  tabValue: string;
  sortByvalue: string;
  searchBy: string;
  sortByOrder: string;
  activeTab: string;
}

interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: { customers: BuyerDetails[], totalElements: number, pageSize: number, currentPage: number };
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  try {
    const week = 4;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "0");
    const tabValue = url.searchParams.get("tabValue") || "all";
    const sortBy = url.searchParams.get("sortBy") || "buyerName";
    const searchBy = url.searchParams.get("searchBy") || "";
    const sortByOrder = url.searchParams.get("sortByOrder") || "asc";
    const activeTab = url.searchParams.get("activeTab") || "Customer Overview";
    const validSearchBy = searchBy && searchBy.length >= 3 ? searchBy : "";
    const pageSize = 20;

    let salesResponse, conversionResponse, acquisitionResponse, customersResponse;
    const responseHeaders = new Headers();

    // Conditionally call APIs based on active tab
    if (activeTab === "Customer Overview") {
      // For Customer Overview tab, call analytics APIs
      const [salesRes, conversionRes, acquisitionRes] = await Promise.all([
        getSellerSales(week, request),
        getConversionRate(3, request),
        getAcquiSitionRates(week, request)
      ]);

      salesResponse = salesRes;
      conversionResponse = conversionRes;
      acquisitionResponse = acquisitionRes;

      // Set response headers
      [salesResponse, conversionResponse, acquisitionResponse].forEach(response => {
        if (response.headers?.has('Set-Cookie')) {
          responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
        }
      });

      return withResponse({
        sales: salesResponse.data,
        customerConversionRates: conversionResponse.data,
        customerAcquisitionRate: acquisitionResponse.data,
        week: week,
        // Empty customer data for overview tab
        customers: [],
        currentPage: 0,
        hasNextPage: false,
        hasMoreData: false,
        tabValue: "all",
        sortByvalue: "buyerName",
        searchBy: "",
        sortByOrder: "asc",
        activeTab
      }, responseHeaders);

    } else if (activeTab === "Customer List") {
      // For Customer List tab, call buyer summary API
      customersResponse = await getBuyerSummary(user.userId, page, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);

      const hasNextPage = (customersResponse.data?.length ?? 0) >= pageSize;
      const nextPageResponse = await getBuyerSummary(user.userId, page + 1, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);
      const hasMoreData = (nextPageResponse.data ?? []).length > 0;

      // Set response headers
      if (customersResponse.headers?.has('Set-Cookie')) {
        responseHeaders.set('Set-Cookie', customersResponse.headers.get('Set-Cookie')!);
      }

      return withResponse({
        // Empty analytics data for customer list tab
        sales: null,
        customerConversionRates: null,
        customerAcquisitionRate: null,
        week: week,
        // Customer List Data
        customers: customersResponse.data,
        currentPage: page,
        hasNextPage,
        hasMoreData,
        tabValue,
        sortByvalue: sortBy,
        searchBy,
        sortByOrder,
        activeTab
      }, responseHeaders);
    } else {
      return withResponse({
        sales: null,
        customerConversionRates: null,
        customerAcquisitionRate: null,
        week: week,
        customers: [],
        currentPage: 0,
        hasNextPage: false,
        hasMoreData: false,
        tabValue: "all",
        sortByvalue: "buyerName",
        searchBy: "",
        sortByOrder: "asc",
        activeTab
      }, responseHeaders);
    }
  }
  catch (error) {
    throw new Response("Failed to fetch customer analysis data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }

  if (!user.sellerId) {
    return json({ success: false, errorMessage: "Seller ID not found", intent: intent }, { status: 400 });
  }

  if (intent === "UpdateFbDiscount") {
    const nBuyerId = Number(formData.get("nBuyerId"))
    const fBDiscount = Number(formData.get("fbDiscount"))

    try {
      const response = await updateFbDiscountPrice(nBuyerId, fBDiscount, request);
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update discount" }, { status: 400 })
    }
  }

  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});

const mockCustomers = [
  {
    id: "CUST-001",
    hashedId: "h4s8d92j1k",
    firstName: "Rahul",
    lastName: "Sharma",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    city: "Bangalore",
    pincode: "560001",
    geo: { lat: 12.9716, lng: 77.5946 },
    firstOrder: "2024-10-15",
    latestOrder: "2025-01-14",
    lifetimeOrders: 47,
    lifetimeGMV: 18950,
    averageAOV: 403,
    preferredChannel: "Website",
    preferredOutlet: "Downtown",
    rfmScores: { recency: 5, frequency: 4, monetary: 4 },
    tags: ["VIP", "Regular"],
    churnRisk: 12,
    loyaltyPoints: { earned: 1895, redeemed: 450, balance: 1445, expiry: "2025-06-15" },
    consents: {
      whatsapp: true,
      sms: false,
      email: true,
      doNotDisturb: false
    },
    segment: "Champions",
    joinDate: "2024-10-15"
  },
  {
    id: "CUST-002",
    hashedId: "k2j9f8l3m",
    firstName: "Priya",
    lastName: "Patel",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    city: "Bangalore",
    pincode: "560025",
    geo: { lat: 12.9352, lng: 77.6245 },
    firstOrder: "2024-12-01",
    latestOrder: "2025-01-13",
    lifetimeOrders: 12,
    lifetimeGMV: 4680,
    averageAOV: 390,
    preferredChannel: "WhatsApp",
    preferredOutlet: "Mall Road",
    rfmScores: { recency: 4, frequency: 3, monetary: 3 },
    tags: ["New Customer"],
    churnRisk: 25,
    loyaltyPoints: { earned: 468, redeemed: 100, balance: 368, expiry: "2025-04-01" },
    consents: {
      whatsapp: true,
      sms: true,
      email: false,
      doNotDisturb: false
    },
    segment: "Potential Loyalists",
    joinDate: "2024-12-01"
  },
  {
    id: "CUST-003",
    hashedId: "m8n4p9q2r",
    firstName: "Arjun",
    lastName: "Mehta",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    city: "Bangalore",
    pincode: "560034",
    geo: { lat: 12.9279, lng: 77.6271 },
    firstOrder: "2024-08-20",
    latestOrder: "2024-12-15",
    lifetimeOrders: 8,
    lifetimeGMV: 2240,
    averageAOV: 280,
    preferredChannel: "Instagram",
    preferredOutlet: "Downtown",
    rfmScores: { recency: 2, frequency: 2, monetary: 2 },
    tags: ["At Risk"],
    churnRisk: 78,
    loyaltyPoints: { earned: 224, redeemed: 0, balance: 224, expiry: "2025-02-20" },
    consents: {
      whatsapp: false,
      sms: false,
      email: true,
      doNotDisturb: false
    },
    segment: "At Risk",
    joinDate: "2024-08-20"
  },
  {
    id: "CUST-004",
    hashedId: "r7s5t2u9v",
    firstName: "Sneha",
    lastName: "Gupta",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    city: "Bangalore",
    pincode: "560002",
    geo: { lat: 12.9698, lng: 77.7500 },
    firstOrder: "2024-06-10",
    latestOrder: "2025-01-12",
    lifetimeOrders: 89,
    lifetimeGMV: 34560,
    averageAOV: 388,
    preferredChannel: "Website",
    preferredOutlet: "Mall Road",
    rfmScores: { recency: 5, frequency: 5, monetary: 5 },
    tags: ["VIP", "Loyal", "High Value"],
    churnRisk: 5,
    loyaltyPoints: { earned: 3456, redeemed: 1200, balance: 2256, expiry: "2025-12-10" },
    consents: {
      whatsapp: true,
      sms: true,
      email: true,
      doNotDisturb: false
    },
    segment: "Champions",
    joinDate: "2024-06-10"
  }
];

const customerSegments = [
  { name: "Champions", count: 156, percentage: 12.4, color: "#00A390" },
  { name: "Loyal Customers", count: 289, percentage: 23.1, color: "#28a745" },
  { name: "Potential Loyalists", count: 234, percentage: 18.7, color: "#17a2b8" },
  { name: "New Customers", count: 198, percentage: 15.8, color: "#6c757d" },
  { name: "At Risk", count: 167, percentage: 13.3, color: "#ffc107" },
  { name: "Cannot Lose Them", count: 98, percentage: 7.8, color: "#fd7e14" },
  { name: "Hibernating", count: 89, percentage: 7.1, color: "#DC3545" },
  { name: "Lost", count: 23, percentage: 1.8, color: "#6f42c1" }
];

const loyaltyTiers = [
  { tier: "Diamond", customers: 45, minSpend: 25000, benefits: "20% off, Free delivery, Priority support" },
  { tier: "Gold", customers: 123, minSpend: 15000, benefits: "15% off, Free delivery" },
  { tier: "Silver", customers: 267, minSpend: 8000, benefits: "10% off" },
  { tier: "Bronze", customers: 578, minSpend: 0, benefits: "5% off" }
];

const retentionData = [
  { month: "Aug", newCustomers: 45, retained: 35, churnRate: 22 },
  { month: "Sep", newCustomers: 52, retained: 41, churnRate: 21 },
  { month: "Oct", newCustomers: 61, retained: 48, churnRate: 21 },
  { month: "Nov", newCustomers: 58, retained: 47, churnRate: 19 },
  { month: "Dec", newCustomers: 67, retained: 56, churnRate: 16 },
  { month: "Jan", newCustomers: 73, retained: 62, churnRate: 15 }
];

export default function SellerCustomers() {
  const { sales, customerConversionRates, customerAcquisitionRate, week, customers, currentPage, tabValue, sortByvalue, searchBy, sortByOrder, activeTab: loaderActiveTab } = useLoaderData<LoaderData>();
  const [selectedCustomer, setSelectedCustomer] = useState<BuyerDetails | null>(null)
  const [activeTab, setActiveTab] = useState<"Customer Overview" | "Customer List" | "Customer Loyalty">(
    (loaderActiveTab as "Customer Overview" | "Customer List" | "Customer Loyalty") || "Customer Overview"
  )
  const fetcher = useFetcher<ActionData>()
  const [searchParams, setSearchParams] = useSearchParams()

  // action
  const [actionType, setActionType] = useState<string>("")
  const [actionSelectedCustomer, setActionSelectedCustomer] = useState<BuyerDetails | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Search and Filters
  const [searchType, setSearchType] = useState<"ID" | "Mobile" | "Name">("ID")
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [filterDate, setFilterDate] = useState<DateRange | undefined>(undefined);

  // local state
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  // Customer List specific state
  const [customerListSearchTerm, setCustomerListSearchTerm] = useState(searchBy)
  const [customerListSortBy, setCustomerListSortBy] = useState(sortByvalue)
  const [customerListSortOrder, setCustomerListSortOrder] = useState(sortByOrder)
  const [customerListActiveTab, setCustomerListActiveTab] = useState(tabValue)
  const [fbDiscounts, setFbDiscounts] = useState<{ [key: number]: string }>({});
  const [isFbDis, setIsFbDis] = useState<{ [key: number]: boolean }>({});

  // debounce on search term
  useEffect(() => {
    const timer = setTimeout(() => {
      // This effect is now used for the search functionality
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);

  // Navigation function to update URL params and trigger reload
  const updateCustomerListParams = (params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });
    setSearchParams(newSearchParams);
  };

  const handleTabChange = (newTab: string) => {
    setSearchType("ID")
    setSearchTerm("")
    setFilterDate(undefined)
    setDateRange(undefined)
    setActiveTab(newTab as "Customer Overview" | "Customer List" | "Customer Loyalty")

    // Update URL params to trigger loader with activeTab
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("activeTab", newTab);

    // Reset customer list specific params when switching tabs
    if (newTab !== "Customer List") {
      newSearchParams.delete("page");
      newSearchParams.delete("tabValue");
      newSearchParams.delete("sortBy");
      newSearchParams.delete("sortByOrder");
      newSearchParams.delete("searchBy");
    }

    setSearchParams(newSearchParams);
  }

  const handleAction = (customer: BuyerDetails, action: string) => {
    setActionSelectedCustomer(customer)
    setActionType(action)
  }

  const handleSubmitAction = (formData: Record<string, unknown>) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({ customer: actionSelectedCustomer, formData }))
    fetcher.submit(actionData, { method: "post" })
    setIsSubmitting(true)
  }

  // Customer List specific handlers
  const handleCustomerListTabChange = (newTab: string) => {
    const tabMap: { [key: string]: string } = {
      all: "all",
      oneOrder: "one_order",
      frequent: "frequent_orders",
      zero_orders: "zero_orders",
    };
    const validTabValue = tabMap[newTab] || "all";
    setCustomerListActiveTab(newTab);

    // Update URL params to trigger API call
    updateCustomerListParams({
      activeTab: "Customer List",
      tabValue: validTabValue,
      page: "0", // Reset to first page when changing tabs
      sortBy: customerListSortBy,
      sortByOrder: customerListSortOrder,
      searchBy: customerListSearchTerm
    });
  };

  const handleCustomerListSort = (newSort: string) => {
    setCustomerListSortBy((prevSortBy) => {
      const isSameSort = prevSortBy === newSort;
      setCustomerListSortOrder((prevSortOrder) => {
        const newOrder = isSameSort && prevSortOrder === "asc" ? "desc" : "asc";

        // Update URL params to trigger API call
        updateCustomerListParams({
          activeTab: "Customer List",
          tabValue: customerListActiveTab,
          page: "0", // Reset to first page when sorting
          sortBy: newSort,
          sortByOrder: newOrder,
          searchBy: customerListSearchTerm
        });

        return newOrder;
      });
      return newSort;
    });
  };

  const getSortIcon = (column: string) => {
    if (customerListSortBy !== column) return null;
    return customerListSortOrder === "asc" ? <ArrowUp className="w-4 h-4 ml-1" /> : <ArrowDown className="w-4 h-4 ml-1" />;
  };

  const handleCustomerListSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomerListSearchTerm(value);

    // Update URL params to trigger API call (with debounce)
    setTimeout(() => {
      updateCustomerListParams({
        activeTab: "Customer List",
        tabValue: customerListActiveTab,
        page: "0", // Reset to first page when searching
        sortBy: customerListSortBy,
        sortByOrder: customerListSortOrder,
        searchBy: value.length >= 3 ? value : ""
      });
    }, 300);
  };

  const handleChangeFbDiscount = (nBuyerId: number, val: string) => {
    if (/^\d*\.?\d*$/.test(val) || val === "") {
      setFbDiscounts((prev) => ({ ...prev, [nBuyerId]: val }));
    }
  };

  const fbFetcher = useFetcher<BuyerSummaryDetailsResponseItem>()

  const handleSave = (nBuyerId: number) => {
    const formData = new FormData()
    formData.append("intent", "UpdateFbDiscount");
    formData.append("nBuyerId", nBuyerId.toString());
    formData.append("fbDiscount", fbDiscounts[nBuyerId]);
    fbFetcher.submit(formData, { method: "POST" })
    setIsFbDis((prev) => ({ ...prev, [nBuyerId]: false }));
  };

  // Handle pagination changes

  const handlePageChange = (newPage: number) => {
    updateCustomerListParams({
      activeTab: "Customer List",
      page: newPage.toString()
    });
  };

  useEffect(() => {
    if (fetcher.data?.intent === "UpdateFbDiscount") {
      if (fetcher.data?.success) {
        toast({
          title: "Success",
          description: "Discount updated successfully",
        })
      } else if (fetcher.data?.success === false) {
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
  }, [fetcher.data, toast])

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto mb-6">

        {/* Header */}
        <div className="mb-4">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl md:text-3xl font-bold text-gray-900">Customers</h1>
              <p className="mt-2 text-gray-600">Manage your customer relationships and loyalty programs</p>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="w-full h-10 mb-1">
            <TabsTrigger value="Customer Overview" className="w-1/2 h-8 py-1 font-semibold">Customer Overview</TabsTrigger>
            <TabsTrigger value="Customer List" className="w-1/2 h-8 py-1 font-semibold">Customer List</TabsTrigger>
            <TabsTrigger value="Customer Loyalty" className="w-1/2 h-8 py-1 font-semibold">Customer Loyalty</TabsTrigger>
          </TabsList>

          <TabsContent value="Customer Overview">
            <div className="w-full min-h-screen">
              <main className="p-2">
                {sales && (
                  <section>
                    <CustomerSales sales={sales} />
                  </section>
                )}
                {customerConversionRates && (
                  <section>
                    <ConversionRate conversionRates={customerConversionRates} week={week} />
                  </section>
                )}
                {customerAcquisitionRate && (
                  <section>
                    <CustomersAcquisition customerAcquisitionRate={customerAcquisitionRate} />
                  </section>
                )}
              </main>
            </div>
            {/* <div className="space-y-5">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="space-y-4">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold">Total Customers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,254</div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-green-600">+12.3%</span>
                    <span className="ml-1">vs last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="space-y-4">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold">Avg Lifetime Value</CardTitle>
                  <Star className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹15,234</div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-green-600">+8.7%</span>
                    <span className="ml-1">vs last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="space-y-4">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold">Retention Rate</CardTitle>
                  <Heart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">85%</div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-green-600">+2.1%</span>
                    <span className="ml-1">vs last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="space-y-4">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold">Churn Risk</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">15%</div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <TrendingDown className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-green-600">-3.2%</span>
                    <span className="ml-1">improvement</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-normal">Customer Segments</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={customerSegments}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="count"
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                      >
                        {customerSegments.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-normal">Customer Retention Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={retentionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="newCustomers" stroke="#00A390" strokeWidth={2} name="New Customers" />
                      <Line type="monotone" dataKey="retained" stroke="#28a745" strokeWidth={2} name="Retained" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-normal">Customer Segments Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {customerSegments.slice(0, 4).map((segment) => (
                    <div key={segment.name} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{segment.name}</h4>
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: segment.color }}
                        />
                      </div>
                      <div className="space-y-1">
                        <p className="text-2xl font-bold">{segment.count}</p>
                        <p className="text-sm text-muted-foreground">{segment.percentage}% of total</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div> */}
          </TabsContent>

          <TabsContent value="Customer List">
            {/* Search and Filters */}
            {/* <Card className="mb-1 bg-gray-100">
              <CardContent className="p-2">
                <div className="space-y-1">
                  <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-2">
                    <div className="relative lg:col-span-1">
                      <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Search by" />
                        </SelectTrigger>
                        <SelectContent>
                          {searchTypeFilters.map((filter) => (
                            <SelectItem key={filter.value} value={filter.value}>
                              {filter.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="relative lg:col-span-2">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder={searchType === "Name" ? "Search name" : (searchType === "Mobile") ? "Search by Mobile" : "Search by ID"}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>

                    <div className="relative sm:col-span-2 lg:col-span-1 ">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="filterDate"
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !filterDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon />
                            {filterDate?.from ? (
                              filterDate.to ? (
                                <>
                                  {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                                </>
                              ) : (
                                format(filterDate.from, "LLL dd, y")
                              )
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            initialFocus
                            selected={dateRange}
                            mode="range"
                            onSelect={(range: DateRange | undefined) => {
                              if (!range?.from) return;
                              setDateRange({
                                from: range.from,
                                to: range.to || undefined,
                              });
                            }}
                          />
                          <PopoverClose className="w-full">
                            <Button
                              variant="ghost"
                              className="w-full text-blue-500 hover:text-blue-500 justify-center"
                              onClick={() => setFilterDate(dateRange)}
                            >
                              Set
                            </Button>
                          </PopoverClose>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                 
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                    <div className="space-y-1 lg:col-start-4 flex items-end">
                      <Button
                        onClick={() => {
                          setSearchType("ID")
                          setSearchTerm("")
                          setFilterDate(undefined)
                          setDateRange(undefined)
                          setPageSize(20)
                        }}
                        className="w-full"
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        Clear Filters
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Customer List Tabs */}
            <Card className="mb-1">
              <CardContent className="p-2">
                <Tabs value={customerListActiveTab} onValueChange={handleCustomerListTabChange} className="mb-4">
                  <TabsList className="w-full mb-1 grid grid-cols-2 lg:grid-cols-4 h-20 lg:h-10">
                    <TabsTrigger value="all" className="h-8 py-1"> All Customers</TabsTrigger>
                    <TabsTrigger value="frequent" className="h-8 py-1"> Frequent Customers</TabsTrigger>
                    <TabsTrigger value="oneOrder" className="h-8 py-1"> One Order Customers</TabsTrigger>
                    <TabsTrigger value="zero_orders" className="h-8 py-1"> New Customers</TabsTrigger>
                  </TabsList>
                </Tabs>

                {/* Customer Search */}
                <div className="flex justify-between mb-4">
                  <Input
                    placeholder="Search by name or owner"
                    value={customerListSearchTerm}
                    onChange={handleCustomerListSearch}
                    className="max-w-sm"
                  />
                  <Select value={customerListSortBy} onValueChange={handleCustomerListSort}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="buyerName">Name</SelectItem>
                      <SelectItem value="totalOrders">Number of Orders</SelectItem>
                      <SelectItem value="pendingAmount">Pending Balance</SelectItem>
                      <SelectItem value="lastOrderedDate">Order Duration Days</SelectItem>
                      <SelectItem value="fbDiscount">F.B.Discount</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Customer Table */}
                <Table>
                  {fbFetcher.state !== "idle" && <SpinnerLoader loading={true} />}
                  <TableHeader>
                    <TableRow>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("buyerName")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Name</span>
                          <span>{getSortIcon("buyerName")}</span>
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("totalOrders")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Num of Orders</span>
                          {getSortIcon("totalOrders")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("pendingAmount")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Pending Balance</span>
                          {getSortIcon("pendingAmount")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("lastOrderedDate")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Order Duration Days</span>
                          {getSortIcon("lastOrderedDate")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("fbDiscount")}>
                        <span className='flex flex-row items-center gap-1'><span>Freq Buyer Discount</span> {getSortIcon("fbDiscount")} </span>
                      </TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {customers.map((customer) => (
                      <TableRow key={customer.buyerId}>
                        <TableCell>
                          <div className='flex flex-row gap-2 items-center'>
                            <div className="text-blue-600 hover:underline cursor-pointer">
                              <div className="break-all">{customer.buyerName !== "" ? customer.buyerName : "( Name not given )"}</div>
                            </div>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Info size={18} className="cursor-pointer text-gray-600 hover:text-purple-600 transition-all" />
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-fit p-5 rounded-lg">
                                <DialogHeader>
                                  <DialogTitle className="text-lg font-semibold text-gray-800">About Customer</DialogTitle>
                                </DialogHeader>
                                <div className="flex flex-col gap-3 mt-2">
                                  <div className="flex flex-row gap-2">
                                    <span className="text-sm text-gray-600">Buyer Name:</span>
                                    <span className="text-base text-purple-800 font-semibold">{customer.buyerName}</span>
                                  </div>
                                  <div className="flex flex-row gap-2">
                                    <span className="text-sm text-gray-600">Mobile Number:</span>
                                    <span className="text-base text-purple-800 font-semibold">{customer.mobileNumber}</span>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                        <TableCell>{customer.totalOrders}</TableCell>
                        <TableCell>₹ {customer.pendingAmount.toLocaleString('en-IN')}</TableCell>
                        <TableCell>
                          {customer.lastOrderedDate
                            ? (isNaN(new Date(customer.lastOrderedDate).getTime())
                              ? '-'
                              : Math.floor((new Date().getTime() - new Date(customer.lastOrderedDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days')
                            : '-'}
                        </TableCell>
                        <TableCell className='flex flex-row items-center gap-2'>
                          {isFbDis[customer.nBuyerId] ? (
                            <div className="flex flex-row justify-center items-center gap-3">
                              <Input
                                value={String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? "")}
                                onChange={(e) => {
                                  const val = e.target.value;
                                  if (!/^\d*\.?\d*$/.test(val)) return;
                                  let numVal = parseFloat(val);
                                  if (numVal > 100) numVal = 100;
                                  if (numVal < 0 || isNaN(numVal)) numVal = 0;
                                  handleChangeFbDiscount(customer.nBuyerId, String(numVal));
                                }}
                                disabled={!isFbDis[customer.nBuyerId]}
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                              />
                              <Save
                                size={24}
                                onClick={() => handleSave(customer.nBuyerId)}
                                className="cursor-pointer text-green-500"
                              />
                              <X
                                color="red"
                                size={24}
                                className="cursor-pointer text-red-500"
                                onClick={() => setIsFbDis({})}
                              />
                            </div>
                          ) : (
                            <div className='flex flex-row gap-3 items-center justify-center'>
                              <span>{customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : "-"}</span>
                              <Pencil
                                size={15}
                                onClick={() => setIsFbDis({ [customer.nBuyerId]: true })}
                                className="cursor-pointer text-blue-500"
                              />
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Arrow Pagination */}
                <Card className="p-0 mb-1">
                  <CardContent className="px-1.5 py-1">
                    <div className="flex flex-row items-center justify-between gap-1.5">
                      <div className="text-sm text-gray-600">
                        Page {currentPage + 1}
                      </div>
                      <div className="flex flex-row items-center gap-1.5">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 0}
                          className="h-[36px] w-[36px] p-0"
                        >
                          <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={customers.length === 0 || customers.length < 20}
                          className="h-[36px] w-[36px] p-0"
                        >
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="Customer Loyalty">
            <div className="space-y-5">
              {/* Loyalty Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="space-y-4">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold">Active Members</CardTitle>
                    <Gift className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">1,013</div>
                    <p className="text-xs text-muted-foreground">80.7% of total customers</p>
                  </CardContent>
                </Card>

                <Card className="space-y-4">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold">Points Issued</CardTitle>
                    <Star className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2.4M</div>
                    <p className="text-xs text-muted-foreground">This month</p>
                  </CardContent>
                </Card>

                <Card className="space-y-4">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold">Redemption Rate</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">34%</div>
                    <p className="text-xs text-green-600">+5.2% vs last month</p>
                  </CardContent>
                </Card>
              </div>

              {/* Loyalty Tiers */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-normal">Loyalty Tiers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {loyaltyTiers.map((tier) => (
                      <div key={tier.tier} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${tier.tier === 'Diamond' ? 'bg-purple-100 text-purple-600' :
                            tier.tier === 'Gold' ? 'bg-yellow-100 text-yellow-600' :
                              tier.tier === 'Silver' ? 'bg-gray-100 text-gray-600' :
                                'bg-orange-100 text-orange-600'
                            }`}>
                            <Star className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">{tier.tier}</h4>
                            <p className="text-sm text-muted-foreground">Min spend: ₹{tier.minSpend.toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">{tier.benefits}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold">{tier.customers}</p>
                          <p className="text-xs text-muted-foreground">customers</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Customer Details Modal */}
        <CustomerDetailsModal customer={selectedCustomer} onClose={() => setSelectedCustomer(null)} onAction={handleAction} />

        {/* Action Modal */}
        <ActionModal
          customer={actionSelectedCustomer}
          actionType={actionType}
          onClose={() => {
            setActionSelectedCustomer(null)
            setActionType("")
          }}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmitAction}
        />
      </div>
    </div >
  )
}


// Customer Details Modal
interface CustomerDetailsModalProps {
  customer: BuyerDetails | null
  onClose: () => void
  onAction: (customer: BuyerDetails, action: string) => void
}

export function CustomerDetailsModal({ customer, onClose }: CustomerDetailsModalProps) {
  if (!customer) return null

  const handlePhoneClick = (phoneNumber: string) => {
    window.open(`tel:${phoneNumber}`, "_self")
  }

  return (
    <Dialog open={!!customer} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
            Customer Details - #{customer.buyerId}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span className="font-medium">{customer.buyerName}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>{customer.mobileNumber}</span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handlePhoneClick(customer.mobileNumber)}
                className="h-6 w-6 p-0"
              >
                <Phone className="w-3 h-3" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span>{customer.address}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


// Action Modal
interface ActionModalProps {
  customer: BuyerDetails | null
  actionType: string
  onClose: () => void
  isSubmitting: boolean
  onSubmit: (formData: Record<string, unknown>) => void
}

export function ActionModal({ customer, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {
  const [formData] = useState({
    reason: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  if (!customer || !actionType) return null

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl rounded-md">
        <DialogHeader>
          <DialogTitle>
            Action Modal
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Processing..." : "Confirm"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}