import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Label } from "~/components/ui/label";
import {
  Megaphone,
  Plus,
  Edit,
  Eye,
  Play,
  Pause,
  Copy,
  Target,
  TrendingUp,
  DollarSign,
  Percent,
  Clock,
  Tag,
  Zap,
  BarChart3,
  Search,
  XCircle,
} from "lucide-react";
import { <PERSON><PERSON><PERSON>, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { format, addDays } from "date-fns";

const mockCampaigns = [
  {
    id: "CAM-001",
    name: "Weekend Special 25% Off",
    type: "Promotional Offer",
    description: "Weekend discount campaign for all menu items",
    status: "Active",
    startDate: "2025-01-13",
    endDate: "2025-01-19",
    targetAudience: "All Customers",
    channels: ["WhatsApp", "Email", "Push"],
    budget: 5000,
    spent: 1250,
    impressions: 12500,
    clicks: 875,
    conversions: 156,
    revenue: 23400,
    roi: 368,
    promoCode: "WEEKEND25",
    discountType: "Percentage",
    discountValue: 25,
    minOrderValue: 299,
    maxDiscount: 200,
    usageLimit: 1000,
    usageCount: 156,
    createdBy: "Rajesh Kumar",
    createdAt: "2025-01-12T10:30:00Z",
    outlets: ["Downtown", "Mall Road"]
  },
  {
    id: "CAM-002",
    name: "New Customer Welcome",
    type: "Welcome Campaign",
    description: "₹100 off for first-time customers",
    status: "Active",
    startDate: "2025-01-01",
    endDate: "2025-03-31",
    targetAudience: "New Customers",
    channels: ["WhatsApp", "Email"],
    budget: 10000,
    spent: 4500,
    impressions: 8900,
    clicks: 445,
    conversions: 89,
    revenue: 17800,
    roi: 296,
    promoCode: "WELCOME100",
    discountType: "Fixed Amount",
    discountValue: 100,
    minOrderValue: 399,
    maxDiscount: 100,
    usageLimit: 500,
    usageCount: 89,
    createdBy: "Priya Sharma",
    createdAt: "2024-12-28T14:20:00Z",
    outlets: ["Downtown", "Mall Road"]
  },
  {
    id: "CAM-003",
    name: "Lunch Hour Flash Sale",
    type: "Time-based Campaign",
    description: "Buy 1 Get 1 on selected items during lunch hours",
    status: "Paused",
    startDate: "2025-01-10",
    endDate: "2025-01-20",
    targetAudience: "Repeat Customers",
    channels: ["Push", "WhatsApp"],
    budget: 3000,
    spent: 1800,
    impressions: 6700,
    clicks: 335,
    conversions: 67,
    revenue: 9450,
    roi: 425,
    promoCode: "LUNCH_BOGO",
    discountType: "Buy X Get Y",
    discountValue: 50,
    minOrderValue: 199,
    maxDiscount: 150,
    usageLimit: 200,
    usageCount: 67,
    createdBy: "Amit Singh",
    createdAt: "2025-01-08T09:15:00Z",
    outlets: ["Downtown"]
  },
  {
    id: "CAM-004",
    name: "Loyalty Points Double",
    type: "Loyalty Campaign",
    description: "Earn 2x loyalty points on all orders",
    status: "Scheduled",
    startDate: "2025-01-20",
    endDate: "2025-01-27",
    targetAudience: "Loyalty Members",
    channels: ["Email", "Push"],
    budget: 2000,
    spent: 0,
    impressions: 0,
    clicks: 0,
    conversions: 0,
    revenue: 0,
    roi: 0,
    promoCode: "DOUBLE_POINTS",
    discountType: "Loyalty Multiplier",
    discountValue: 2,
    minOrderValue: 199,
    maxDiscount: 0,
    usageLimit: 1000,
    usageCount: 0,
    createdBy: "Sneha Gupta",
    createdAt: "2025-01-14T16:45:00Z",
    outlets: ["Downtown", "Mall Road"]
  },
  {
    id: "CAM-005",
    name: "Republic Day Special",
    type: "Seasonal Campaign",
    description: "Special menu and pricing for Republic Day",
    status: "Draft",
    startDate: "2025-01-26",
    endDate: "2025-01-26",
    targetAudience: "All Customers",
    channels: ["WhatsApp", "Email", "SMS"],
    budget: 8000,
    spent: 0,
    impressions: 0,
    clicks: 0,
    conversions: 0,
    revenue: 0,
    roi: 0,
    promoCode: "REPUBLIC26",
    discountType: "Percentage",
    discountValue: 26,
    minOrderValue: 500,
    maxDiscount: 300,
    usageLimit: 500,
    usageCount: 0,
    createdBy: "Rajesh Kumar",
    createdAt: "2025-01-14T11:20:00Z",
    outlets: ["Downtown", "Mall Road"]
  }
];

const campaignPerformanceData = [
  { date: "Jan 8", impressions: 2100, clicks: 147, conversions: 23, revenue: 4600 },
  { date: "Jan 9", impressions: 2450, clicks: 171, conversions: 34, revenue: 6800 },
  { date: "Jan 10", impressions: 2800, clicks: 196, conversions: 41, revenue: 8200 },
  { date: "Jan 11", impressions: 3200, clicks: 224, conversions: 48, revenue: 9600 },
  { date: "Jan 12", impressions: 2900, clicks: 203, conversions: 37, revenue: 7400 },
  { date: "Jan 13", impressions: 3500, clicks: 245, conversions: 52, revenue: 10400 },
  { date: "Jan 14", impressions: 3100, clicks: 217, conversions: 45, revenue: 9000 }
];

const channelPerformanceData = [
  { channel: "WhatsApp", impressions: 8900, clicks: 623, conversions: 156, ctr: 7.0, cvr: 25.0 },
  { channel: "Email", impressions: 6700, clicks: 469, conversions: 89, ctr: 7.0, cvr: 19.0 },
  { channel: "Push", impressions: 5200, clicks: 312, conversions: 67, ctr: 6.0, cvr: 21.5 },
  { channel: "SMS", impressions: 3400, clicks: 204, conversions: 34, ctr: 6.0, cvr: 16.7 }
];

const audienceSegments = [
  { id: "new_customers", name: "New Customers", count: 1247, description: "First-time visitors" },
  { id: "repeat_customers", name: "Repeat Customers", count: 892, description: "2+ orders in last 30 days" },
  { id: "high_value", name: "High Value", count: 234, description: "AOV > ₹1000" },
  { id: "loyalty_members", name: "Loyalty Members", count: 567, description: "Enrolled in loyalty program" },
  { id: "inactive", name: "Inactive", count: 345, description: "No orders in last 60 days" }
];

const campaignTemplates = [
  {
    id: "welcome_discount",
    name: "Welcome Discount",
    type: "Welcome Campaign",
    description: "Discount for first-time customers",
    defaultDiscount: 15,
    minOrderValue: 299,
    channels: ["WhatsApp", "Email"],
    duration: 30
  },
  {
    id: "weekend_special",
    name: "Weekend Special",
    type: "Promotional Offer",
    description: "Weekend promotional campaigns",
    defaultDiscount: 20,
    minOrderValue: 399,
    channels: ["WhatsApp", "Email", "Push"],
    duration: 2
  },
  {
    id: "loyalty_boost",
    name: "Loyalty Boost",
    type: "Loyalty Campaign",
    description: "Bonus points for loyalty members",
    defaultDiscount: 0,
    minOrderValue: 199,
    channels: ["Email", "Push"],
    duration: 7
  },
  {
    id: "flash_sale",
    name: "Flash Sale",
    type: "Time-based Campaign",
    description: "Limited time offers",
    defaultDiscount: 30,
    minOrderValue: 199,
    channels: ["Push", "WhatsApp"],
    duration: 1
  }
];

export default function Campaigns() {

  const [selectedTab, setSelectedTab] = useState("overview");
  const [selectedCampaign, setSelectedCampaign] = useState<typeof mockCampaigns[0] | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");

  // New campaign form state
  const [newCampaign, setNewCampaign] = useState({
    name: "",
    type: "Promotional Offer",
    description: "",
    startDate: new Date(),
    endDate: addDays(new Date(), 7),
    targetAudience: "All Customers",
    channels: [],
    budget: 5000,
    promoCode: "",
    discountType: "Percentage",
    discountValue: 10,
    minOrderValue: 299,
    maxDiscount: 200,
    usageLimit: 1000,
    outlets: ["Downtown", "Mall Road"]
  });

  const filteredCampaigns = mockCampaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      campaign.promoCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || campaign.status.toLowerCase() === statusFilter;
    const matchesType = typeFilter === "all" || campaign.type.toLowerCase().replace(/\s+/g, "_") === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active": return "default";
      case "Paused": return "secondary";
      case "Scheduled": return "outline";
      case "Draft": return "secondary";
      case "Ended": return "destructive";
      default: return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Active": return <Play className="h-3 w-3" />;
      case "Paused": return <Pause className="h-3 w-3" />;
      case "Scheduled": return <Clock className="h-3 w-3" />;
      case "Draft": return <Edit className="h-3 w-3" />;
      case "Ended": return <XCircle className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const calculateTotals = () => {
    const activeCampaigns = mockCampaigns.filter(c => c.status === "Active");
    return {
      totalCampaigns: mockCampaigns.length,
      activeCampaigns: activeCampaigns.length,
      totalSpent: mockCampaigns.reduce((acc, c) => acc + c.spent, 0),
      totalRevenue: mockCampaigns.reduce((acc, c) => acc + c.revenue, 0),
      totalConversions: mockCampaigns.reduce((acc, c) => acc + c.conversions, 0),
      avgROI: mockCampaigns.reduce((acc, c) => acc + c.roi, 0) / mockCampaigns.length
    };
  };

  const totals = calculateTotals();

  const handleCreateCampaign = () => {
    // Handle campaign creation
    console.log("Creating campaign:", newCampaign);
    setShowCreateDialog(false);
    // Reset form
    setNewCampaign({
      name: "",
      type: "Promotional Offer",
      description: "",
      startDate: new Date(),
      endDate: addDays(new Date(), 7),
      targetAudience: "All Customers",
      channels: [],
      budget: 5000,
      promoCode: "",
      discountType: "Percentage",
      discountValue: 10,
      minOrderValue: 299,
      maxDiscount: 200,
      usageLimit: 1000,
      outlets: ["Downtown", "Mall Road"]
    });
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Campaigns</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.totalCampaigns}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span className="text-green-600">{totals.activeCampaigns}</span>
              <span className="ml-1">active campaigns</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Spend</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalSpent.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+12.5%</span>
              <span className="ml-1">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Revenue Generated</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalRevenue.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+18.3%</span>
              <span className="ml-1">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Average ROI</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.avgROI.toFixed(0)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span className="text-green-600">{totals.totalConversions}</span>
              <span className="ml-1">total conversions</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Campaign Performance Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Campaign Performance Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={campaignPerformanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value, name) => {
                  if (name === 'revenue') return [`₹${value?.toLocaleString()}`, 'Revenue'];
                  return [value?.toLocaleString(), name];
                }} />
                <Legend />
                <Line type="monotone" dataKey="impressions" stroke="#00A390" name="Impressions" />
                <Line type="monotone" dataKey="clicks" stroke="#DB3532" name="Clicks" />
                <Line type="monotone" dataKey="conversions" stroke="#28a745" name="Conversions" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Channel Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Channel Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={channelPerformanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="channel" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="impressions" fill="#00A390" name="Impressions" />
                <Bar dataKey="clicks" fill="#DB3532" name="Clicks" />
                <Bar dataKey="conversions" fill="#28a745" name="Conversions" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-5">
            <div className="flex items-center gap-4">
              <div className="min-w-10 min-h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Plus className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold">Create Campaign</h4>
                <p className="text-sm text-muted-foreground">Start a new marketing campaign</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-5">
            <div className="flex items-center gap-4">
              <div className="min-w-10 min-h-10 bg-red-400/10 rounded-lg flex items-center justify-center">
                <Tag className="h-6 w-6 text-red-400" />
              </div>
              <div>
                <h4 className="font-semibold">Promo Codes</h4>
                <p className="text-sm text-muted-foreground">Manage discount codes</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-5">
            <div className="flex items-center gap-4">
              <div className="min-w-10 min-h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold">Audiences</h4>
                <p className="text-sm text-muted-foreground">Manage customer segments</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-5">
            <div className="flex items-center gap-4">
              <div className="min-w-10 min-h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h4 className="font-semibold">Analytics</h4>
                <p className="text-sm text-muted-foreground">Campaign performance insights</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderCampaigns = () => (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search campaigns or promo codes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="ended">Ended</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="promotional_offer">Promotional Offer</SelectItem>
                <SelectItem value="welcome_campaign">Welcome Campaign</SelectItem>
                <SelectItem value="loyalty_campaign">Loyalty Campaign</SelectItem>
                <SelectItem value="seasonal_campaign">Seasonal Campaign</SelectItem>
                <SelectItem value="time-based_campaign">Time-based Campaign</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Campaign
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Campaign</TableHead>
                <TableHead className="text-black font-semibold">Type</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Performance</TableHead>
                <TableHead className="text-black font-semibold">Budget</TableHead>
                <TableHead className="text-black font-semibold">ROI</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCampaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{campaign.name}</p>
                      <p className="text-sm text-muted-foreground">{campaign.promoCode}</p>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(campaign.startDate), 'MMM d')} - {format(new Date(campaign.endDate), 'MMM d')}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{campaign.type}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(campaign.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(campaign.status)}
                        {campaign.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Eye className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{campaign.impressions.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Target className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{campaign.conversions}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm">₹{campaign.spent.toLocaleString()}</p>
                      <p className="text-xs text-muted-foreground">
                        of ₹{campaign.budget.toLocaleString()}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-green-600">{campaign.roi}%</span>
                      <TrendingUp className="h-3 w-3 text-green-600" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm" onClick={() => setSelectedCampaign(campaign)}>
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        {campaign.status === "Active" ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  const renderAudiences = () => (
    <div className="space-y-6">
      {/* Audience Segments */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-normal">Customer Segments</CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Segment
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {audienceSegments.map((segment) => (
              <Card key={segment.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{segment.name}</h4>
                    <Badge variant="outline">{segment.count.toLocaleString()}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{segment.description}</p>
                  <div className="mt-3">
                    <Button variant="outline" size="sm" className="w-full">
                      <Target className="h-3 w-3 mr-1" />
                      Target Segment
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Segment Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Segment Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={audienceSegments.map(s => ({
              ...s,
              engagement: Math.floor(Math.random() * 100),
              conversion: Math.floor(Math.random() * 50)
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="engagement" fill="#00A390" name="Engagement Rate %" />
              <Bar dataKey="conversion" fill="#DB3532" name="Conversion Rate %" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderTemplates = () => (
    <div className="space-y-6">
      {/* Campaign Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Campaign Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {campaignTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Zap className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <Badge variant="outline" className="mt-1">{template.type}</Badge>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">{template.description}</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Default Discount:</span>
                      <span className="font-medium">{template.defaultDiscount}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Min Order:</span>
                      <span className="font-medium">₹{template.minOrderValue}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span className="font-medium">{template.duration} days</span>
                    </div>
                  </div>
                  <div className="mt-4 flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Copy className="h-3 w-3 mr-1" />
                      Use Template
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-6">
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-gray-900">Campaigns</h1>
        <p className="text-gray-600 mt-2">Manage your marketing campaigns</p>
      </div>

      <div>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid grid-cols-2 lg:grid-cols-4 w-full h-20 lg:h-10">
            <TabsTrigger value="overview" className="font-semibold">Overview</TabsTrigger>
            <TabsTrigger value="campaigns" className="font-semibold">Campaigns</TabsTrigger>
            <TabsTrigger value="audiences" className="font-semibold">Audiences</TabsTrigger>
            <TabsTrigger value="templates" className="font-semibold">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderOverview()}
          </TabsContent>

          <TabsContent value="campaigns">
            {renderCampaigns()}
          </TabsContent>

          <TabsContent value="audiences">
            {renderAudiences()}
          </TabsContent>

          <TabsContent value="templates">
            {renderTemplates()}
          </TabsContent>
        </Tabs>

        {/* Create Campaign Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Campaign</DialogTitle>
              <DialogDescription>
                Set up a new marketing campaign to engage your customers
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Campaign Name</Label>
                  <Input
                    value={newCampaign.name}
                    onChange={(e) => setNewCampaign({ ...newCampaign, name: e.target.value })}
                    placeholder="Enter campaign name"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Campaign Type</Label>
                  <Select
                    value={newCampaign.type}
                    onValueChange={(value) => setNewCampaign({ ...newCampaign, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Promotional Offer">Promotional Offer</SelectItem>
                      <SelectItem value="Welcome Campaign">Welcome Campaign</SelectItem>
                      <SelectItem value="Loyalty Campaign">Loyalty Campaign</SelectItem>
                      <SelectItem value="Seasonal Campaign">Seasonal Campaign</SelectItem>
                      <SelectItem value="Time-based Campaign">Time-based Campaign</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Description</Label>
                <Input
                  value={newCampaign.description}
                  onChange={(e) => setNewCampaign({ ...newCampaign, description: e.target.value })}
                  placeholder="Describe your campaign"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Discount Type</Label>
                  <Select
                    value={newCampaign.discountType}
                    onValueChange={(value) => setNewCampaign({ ...newCampaign, discountType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Percentage">Percentage</SelectItem>
                      <SelectItem value="Fixed Amount">Fixed Amount</SelectItem>
                      <SelectItem value="Buy X Get Y">Buy X Get Y</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Discount Value</Label>
                  <Input
                    type="number"
                    value={newCampaign.discountValue}
                    onChange={(e) => setNewCampaign({ ...newCampaign, discountValue: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Min Order Value</Label>
                  <Input
                    type="number"
                    value={newCampaign.minOrderValue}
                    onChange={(e) => setNewCampaign({ ...newCampaign, minOrderValue: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Usage Limit</Label>
                  <Input
                    type="number"
                    value={newCampaign.usageLimit}
                    onChange={(e) => setNewCampaign({ ...newCampaign, usageLimit: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Promo Code</Label>
                <Input
                  value={newCampaign.promoCode}
                  onChange={(e) => setNewCampaign({ ...newCampaign, promoCode: e.target.value })}
                  placeholder="Enter promo code"
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCampaign}>
                  Create Campaign
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Campaign Detail Dialog */}
        <Dialog open={!!selectedCampaign} onOpenChange={() => setSelectedCampaign(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedCampaign && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Megaphone className="h-6 w-6 text-primary" />
                      <div>
                        <h3>{selectedCampaign.name}</h3>
                        <p className="text-sm text-muted-foreground">{selectedCampaign.promoCode}</p>
                      </div>
                    </div>
                    <Badge variant={getStatusBadgeVariant(selectedCampaign.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(selectedCampaign.status)}
                        {selectedCampaign.status}
                      </div>
                    </Badge>
                  </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Campaign Performance */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Eye className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Impressions</span>
                        </div>
                        <p className="text-2xl font-bold">{selectedCampaign.impressions.toLocaleString()}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Target className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Conversions</span>
                        </div>
                        <p className="text-2xl font-bold">{selectedCampaign.conversions}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Revenue</span>
                        </div>
                        <p className="text-2xl font-bold">₹{selectedCampaign.revenue.toLocaleString()}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">ROI</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600">{selectedCampaign.roi}%</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Campaign Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm font-normal">Campaign Details</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Type:</span>
                          <Badge variant="outline">{selectedCampaign.type}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Start Date:</span>
                          <span>{format(new Date(selectedCampaign.startDate), 'PPP')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>End Date:</span>
                          <span>{format(new Date(selectedCampaign.endDate), 'PPP')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Target Audience:</span>
                          <span>{selectedCampaign.targetAudience}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Channels:</span>
                          <div className="flex gap-1">
                            {selectedCampaign.channels.map(channel => (
                              <Badge key={channel} variant="secondary" className="text-xs">
                                {channel}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm font-normal">Promo Code Details</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Code:</span>
                          <span className="font-mono">{selectedCampaign.promoCode}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span>{selectedCampaign.discountValue}{selectedCampaign.discountType === 'Percentage' ? '%' : '₹'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Min Order:</span>
                          <span>₹{selectedCampaign.minOrderValue}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Max Discount:</span>
                          <span>₹{selectedCampaign.maxDiscount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Usage:</span>
                          <span>{selectedCampaign.usageCount} / {selectedCampaign.usageLimit}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Campaign
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </Button>
                    <Button variant="outline" className="flex-1">
                      {selectedCampaign.status === "Active" ? (
                        <>
                          <Pause className="h-4 w-4 mr-2" />
                          Pause
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Resume
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
} 