import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, UpdateCommand, QueryCommand, GetCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { NotificationLog, NotificationLogTable, CampaignType, MessageCategory, CustomerSegment, NotificationStatus } from '../entities/NotificationLog.js';
import { NotificationQueryFilters } from '../../types/notification.types.js';
import { v4 as uuidv4 } from 'uuid';
import { formatInTimeZone } from 'date-fns-tz';

export class NotificationLogRepository {
    private dynamoDB: DynamoDBDocumentClient;

    constructor() {
        const client = new DynamoDBClient({});
        this.dynamoDB = DynamoDBDocumentClient.from(client);
    }

    /**
     * 🆕 Convert Unix timestamp to proper ISO 8601 format in IST timezone using date-fns-tz
     * Format: 2023-12-30T15:30:45.123+05:30
     */
    private toISTISOString(timestamp: number): string {
        return formatInTimeZone(
            new Date(timestamp),
            'Asia/Kolkata',
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
        );
    }

    /**
     * 🆕 Add ISO timestamps for all timestamp fields
     */
    private enrichWithISOTimestamps(log: Partial<NotificationLog>): Partial<NotificationLog> {
        const enriched = { ...log };

        // Always set these if timestamp fields exist
        if (enriched.timestamp) {
            enriched.timestampISO = this.toISTISOString(enriched.timestamp);
        }
        
        if (enriched.lastUpdated) {
            enriched.lastUpdatedISO = this.toISTISOString(enriched.lastUpdated);
        }

        // Optional analytics timestamps
        if (enriched.sentAt) {
            enriched.sentAtISO = this.toISTISOString(enriched.sentAt);
        }
        
        if (enriched.deliveredAt) {
            enriched.deliveredAtISO = this.toISTISOString(enriched.deliveredAt);
        }
        
        if (enriched.readAt) {
            enriched.readAtISO = this.toISTISOString(enriched.readAt);
        }
        
        if (enriched.failedAt) {
            enriched.failedAtISO = this.toISTISOString(enriched.failedAt);
        }

        return enriched;
    }

    async createLog(log: Omit<NotificationLog, 'notificationId' | 'timestamp' | 'timestampISO' | 'lastUpdatedISO'>): Promise<NotificationLog> {
        const timestamp = Date.now();
        const baseNotificationLog: NotificationLog = {
            ...log,
            notificationId: uuidv4(),
            timestamp,
            timestampISO: this.toISTISOString(timestamp),
            retryCount: 0,
            lastUpdated: timestamp,
            lastUpdatedISO: this.toISTISOString(timestamp)
        };

        // Add ISO timestamps for any existing analytics timestamps
        const enrichedLog = this.enrichWithISOTimestamps(baseNotificationLog) as NotificationLog;

        await this.dynamoDB.send(new PutCommand({
            TableName: NotificationLogTable.TableName,
            Item: enrichedLog
        }));

        return enrichedLog;
    }

    async updateLog(notificationId: string, timestamp: number, updates: Partial<NotificationLog>): Promise<void> {
        const updateTime = Date.now();
        
        // Always update lastUpdated and its ISO timestamp
        const enrichedUpdates = this.enrichWithISOTimestamps({
            ...updates,
            lastUpdated: updateTime,
            lastUpdatedISO: this.toISTISOString(updateTime)
        });

        const updateExpression: string[] = [];
        const expressionAttributeNames: Record<string, string> = {};
        const expressionAttributeValues: Record<string, any> = {};

        Object.entries(enrichedUpdates).forEach(([key, value]) => {
            if (value !== undefined) {
                updateExpression.push(`#${key} = :${key}`);
                expressionAttributeNames[`#${key}`] = key;
                expressionAttributeValues[`:${key}`] = value;
            }
        });

        if (updateExpression.length > 0) {
            await this.dynamoDB.send(new UpdateCommand({
                TableName: NotificationLogTable.TableName,
                Key: {
                    notificationId,
                    // timestamp
                },
                UpdateExpression: `SET ${updateExpression.join(', ')}`,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues
            }));
        }
    }

    async getLogsByBusiness(businessId: string, startTime?: number, endTime?: number): Promise<NotificationLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':businessId': businessId
        };
        
        const expressionAttributeNames: Record<string, string> = {};
        let keyConditionExpression = 'businessId = :businessId';

        if (startTime && endTime) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
            expressionAttributeValues[':startTime'] = startTime;
            expressionAttributeValues[':endTime'] = endTime;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        const result = await this.dynamoDB.send(new QueryCommand({
            TableName: NotificationLogTable.TableName,
            IndexName: 'BusinessIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined
        }));

        return (result.Items || []) as NotificationLog[];
    }

    async getLog(notificationId: string, timestamp: number): Promise<NotificationLog | null> {
        const result = await this.dynamoDB.send(new GetCommand({
            TableName: NotificationLogTable.TableName,
            Key: {
                notificationId,
                timestamp
            }
        }));

        return (result.Item as NotificationLog) || null;
    }

    // 🆕 Enhanced methods for WhatsApp message tracking and campaigns

    /**
     * Find notification log by WhatsApp message ID for webhook updates
     */
    async findByWhatsAppMessageId(messageId: string): Promise<NotificationLog | null> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: 'WhatsAppMessageIndex',
                KeyConditionExpression: 'whatsappMessageId = :messageId',
                ExpressionAttributeValues: {
                    ':messageId': messageId
                },
                Limit: 1
            }));

            return (result.Items?.[0] as NotificationLog) || null;
        } catch (error) {
            console.error('Error finding notification by WhatsApp message ID:', error);
            return null;
        }
    }

    /**
     * Update notification by WhatsApp message ID (used for webhook status updates)
     * 🆕 Now automatically adds ISO timestamps
     */
    async updateByWhatsAppMessageId(messageId: string, updates: Partial<NotificationLog>): Promise<boolean> {
        try {
            const notification = await this.findByWhatsAppMessageId(messageId);
            if (!notification) {
                return false;
            }

            await this.updateLog(notification.notificationId, notification.timestamp, updates);
            return true;
        } catch (error) {
            console.error('Error updating notification by WhatsApp message ID:', error);
            return false;
        }
    }

    /**
     * Get notifications by campaign ID for analytics
     */
    async getByCampaignId(campaignId: string, limit?: number): Promise<NotificationLog[]> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: 'CampaignIndex',
                KeyConditionExpression: 'campaignId = :campaignId',
                ExpressionAttributeValues: {
                    ':campaignId': campaignId
                },
                Limit: limit,
                ScanIndexForward: false // Most recent first
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error getting notifications by campaign ID:', error);
            return [];
        }
    }

    /**
     * Get notifications by customer segment for analytics
     */
    async getByCustomerSegment(
        segment: CustomerSegment, 
        startTime?: number, 
        endTime?: number,
        limit?: number
    ): Promise<NotificationLog[]> {
        try {
            const expressionAttributeValues: Record<string, any> = {
                ':segment': segment
            };
            
            const expressionAttributeNames: Record<string, string> = {};
            let keyConditionExpression = 'customerSegment = :segment';

            if (startTime && endTime) {
                keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
                expressionAttributeValues[':startTime'] = startTime;
                expressionAttributeValues[':endTime'] = endTime;
                expressionAttributeNames['#timestamp'] = 'timestamp';
            }

            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: 'CustomerSegmentIndex',
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                Limit: limit,
                ScanIndexForward: false
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error getting notifications by customer segment:', error);
            return [];
        }
    }

    /**
     * Get notifications by message category for analytics
     */
    async getByMessageCategory(
        category: MessageCategory,
        startTime?: number,
        endTime?: number,
        limit?: number
    ): Promise<NotificationLog[]> {
        try {
            const expressionAttributeValues: Record<string, any> = {
                ':category': category
            };
            
            const expressionAttributeNames: Record<string, string> = {};
            let keyConditionExpression = 'messageCategory = :category';

            if (startTime && endTime) {
                keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
                expressionAttributeValues[':startTime'] = startTime;
                expressionAttributeValues[':endTime'] = endTime;
                expressionAttributeNames['#timestamp'] = 'timestamp';
            }

            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: 'MessageCategoryIndex',
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                Limit: limit,
                ScanIndexForward: false
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error getting notifications by message category:', error);
            return [];
        }
    }

    /**
     * Get notifications by WhatsApp status for delivery analytics
     */
    async getByWhatsAppStatus(
        status: NotificationStatus,
        startTime?: number,
        endTime?: number,
        limit?: number
    ): Promise<NotificationLog[]> {
        try {
            const expressionAttributeValues: Record<string, any> = {
                ':status': status
            };
            
            const expressionAttributeNames: Record<string, string> = {};
            let keyConditionExpression = 'whatsappStatus = :status';

            if (startTime && endTime) {
                keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
                expressionAttributeValues[':startTime'] = startTime;
                expressionAttributeValues[':endTime'] = endTime;
                expressionAttributeNames['#timestamp'] = 'timestamp';
            }

            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: 'WhatsAppStatusIndex',
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                Limit: limit,
                ScanIndexForward: false
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error getting notifications by WhatsApp status:', error);
            return [];
        }
    }

    /**
     * Advanced query method with multiple filters for analytics
     */
    async queryWithFilters(filters: NotificationQueryFilters): Promise<NotificationLog[]> {
        try {
            // Determine which index to use based on filters
            let indexName: string | undefined;
            let keyConditionExpression: string;
            let filterExpression: string = '';
            const expressionAttributeValues: Record<string, any> = {};
            const expressionAttributeNames: Record<string, string> = {};

            // Choose the best index based on available filters
            if (filters.campaignId) {
                indexName = 'CampaignIndex';
                keyConditionExpression = 'campaignId = :campaignId';
                expressionAttributeValues[':campaignId'] = filters.campaignId;
            } else if (filters.businessId) {
                indexName = 'BusinessIndex';
                keyConditionExpression = 'businessId = :businessId';
                expressionAttributeValues[':businessId'] = filters.businessId;
            } else if (filters.messageCategory) {
                indexName = 'MessageCategoryIndex';
                keyConditionExpression = 'messageCategory = :messageCategory';
                expressionAttributeValues[':messageCategory'] = filters.messageCategory;
            } else if (filters.customerSegment) {
                indexName = 'CustomerSegmentIndex';
                keyConditionExpression = 'customerSegment = :customerSegment';
                expressionAttributeValues[':customerSegment'] = filters.customerSegment;
            } else if (filters.whatsappStatus) {
                indexName = 'WhatsAppStatusIndex';
                keyConditionExpression = 'whatsappStatus = :whatsappStatus';
                expressionAttributeValues[':whatsappStatus'] = filters.whatsappStatus;
            } else {
                // Use scan if no indexed field is available
                return this.scanWithFilters(filters);
            }

            // Add time range to key condition if using an index that supports it
            if ((filters.startTimestamp || filters.endTimestamp) && indexName !== 'WhatsAppMessageIndex') {
                if (filters.startTimestamp && filters.endTimestamp) {
                    keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
                    expressionAttributeValues[':startTime'] = filters.startTimestamp;
                    expressionAttributeValues[':endTime'] = filters.endTimestamp;
                    expressionAttributeNames['#timestamp'] = 'timestamp';
                } else if (filters.startTimestamp) {
                    keyConditionExpression += ' AND #timestamp >= :startTime';
                    expressionAttributeValues[':startTime'] = filters.startTimestamp;
                    expressionAttributeNames['#timestamp'] = 'timestamp';
                } else if (filters.endTimestamp) {
                    keyConditionExpression += ' AND #timestamp <= :endTime';
                    expressionAttributeValues[':endTime'] = filters.endTimestamp;
                    expressionAttributeNames['#timestamp'] = 'timestamp';
                }
            }

            // Build filter expression for additional filters
            const filterParts: string[] = [];

            if (filters.status) {
                filterParts.push('#status = :status');
                expressionAttributeNames['#status'] = 'status';
                expressionAttributeValues[':status'] = filters.status;
            }

            if (filters.mobileNumber) {
                filterParts.push('mobileNumber = :mobileNumber');
                expressionAttributeValues[':mobileNumber'] = filters.mobileNumber;
            }

            if (filters.campaignType) {
                filterParts.push('campaignType = :campaignType');
                expressionAttributeValues[':campaignType'] = filters.campaignType;
            }

            if (filters.tags && filters.tags.length > 0) {
                // Check if all tags are present
                filters.tags.forEach((tag, index) => {
                    filterParts.push(`contains(tags, :tag${index})`);
                    expressionAttributeValues[`:tag${index}`] = tag;
                });
            }

            if (filters.hasWebhookLog !== undefined) {
                if (filters.hasWebhookLog) {
                    filterParts.push('attribute_exists(webhookLogId)');
                } else {
                    filterParts.push('attribute_not_exists(webhookLogId)');
                }
            }

            if (filterParts.length > 0) {
                filterExpression = filterParts.join(' AND ');
            }

            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: NotificationLogTable.TableName,
                IndexName: indexName,
                KeyConditionExpression: keyConditionExpression,
                FilterExpression: filterExpression || undefined,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                Limit: filters.limit,
                ExclusiveStartKey: filters.lastEvaluatedKey,
                ScanIndexForward: false
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error querying notifications with filters:', error);
            return [];
        }
    }

    /**
     * Scan method for queries that can't use indexes efficiently
     */
    private async scanWithFilters(filters: NotificationQueryFilters): Promise<NotificationLog[]> {
        try {
            let filterExpression = '';
            const expressionAttributeValues: Record<string, any> = {};
            const expressionAttributeNames: Record<string, string> = {};
            const filterParts: string[] = [];

            // Build filter expression
            if (filters.businessId) {
                filterParts.push('businessId = :businessId');
                expressionAttributeValues[':businessId'] = filters.businessId;
            }

            if (filters.status) {
                filterParts.push('#status = :status');
                expressionAttributeNames['#status'] = 'status';
                expressionAttributeValues[':status'] = filters.status;
            }

            if (filters.campaignType) {
                filterParts.push('campaignType = :campaignType');
                expressionAttributeValues[':campaignType'] = filters.campaignType;
            }

            if (filters.startTimestamp && filters.endTimestamp) {
                filterParts.push('#timestamp BETWEEN :startTime AND :endTime');
                expressionAttributeValues[':startTime'] = filters.startTimestamp;
                expressionAttributeValues[':endTime'] = filters.endTimestamp;
                expressionAttributeNames['#timestamp'] = 'timestamp';
            }

            if (filterParts.length > 0) {
                filterExpression = filterParts.join(' AND ');
            }

            const result = await this.dynamoDB.send(new ScanCommand({
                TableName: NotificationLogTable.TableName,
                FilterExpression: filterExpression || undefined,
                ExpressionAttributeValues: Object.keys(expressionAttributeValues).length > 0 ? expressionAttributeValues : undefined,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                Limit: filters.limit,
                ExclusiveStartKey: filters.lastEvaluatedKey
            }));

            return (result.Items || []) as NotificationLog[];
        } catch (error) {
            console.error('Error scanning notifications with filters:', error);
            return [];
        }
    }

    /**
     * Get campaign analytics aggregation data
     */
    async getCampaignAnalyticsData(campaignId: string): Promise<{
        totalMessages: number;
        statusCounts: Record<NotificationStatus, number>;
        segmentCounts: Record<CustomerSegment, number>;
        timeSeriesData: { timestamp: number; count: number; status: NotificationStatus }[];
    }> {
        try {
            const notifications = await this.getByCampaignId(campaignId);
            
            const statusCounts: Record<NotificationStatus, number> = {
                [NotificationStatus.PENDING]: 0,
                [NotificationStatus.SENT]: 0,
                [NotificationStatus.DELIVERED]: 0,
                [NotificationStatus.READ]: 0,
                [NotificationStatus.FAILED]: 0
            };

            const segmentCounts: Record<CustomerSegment, number> = {
                [CustomerSegment.NEW_CUSTOMER]: 0,
                [CustomerSegment.RETURNING_CUSTOMER]: 0,
                [CustomerSegment.VIP_CUSTOMER]: 0,
                [CustomerSegment.HIGH_VALUE]: 0,
                [CustomerSegment.LOW_ENGAGEMENT]: 0,
                [CustomerSegment.DORMANT]: 0,
                [CustomerSegment.GENERAL]: 0
            };
            const timeSeriesData: { timestamp: number; count: number; status: NotificationStatus }[] = [];

            notifications.forEach(notification => {
                // Count by status
                statusCounts[notification.status]++;

                // Count by segment
                if (notification.customerSegment) {
                    segmentCounts[notification.customerSegment] = (segmentCounts[notification.customerSegment] || 0) + 1;
                }

                // Add to time series (rounded to hour)
                const hourTimestamp = Math.floor(notification.timestamp / 3600000) * 3600000;
                timeSeriesData.push({
                    timestamp: hourTimestamp,
                    count: 1,
                    status: notification.status
                });
            });

            return {
                totalMessages: notifications.length,
                statusCounts,
                segmentCounts,
                timeSeriesData
            };
        } catch (error) {
            console.error('Error getting campaign analytics data:', error);
            return {
                totalMessages: 0,
                statusCounts: {
                    [NotificationStatus.PENDING]: 0,
                    [NotificationStatus.SENT]: 0,
                    [NotificationStatus.DELIVERED]: 0,
                    [NotificationStatus.READ]: 0,
                    [NotificationStatus.FAILED]: 0
                },
                segmentCounts: {
                    [CustomerSegment.NEW_CUSTOMER]: 0,
                    [CustomerSegment.RETURNING_CUSTOMER]: 0,
                    [CustomerSegment.VIP_CUSTOMER]: 0,
                    [CustomerSegment.HIGH_VALUE]: 0,
                    [CustomerSegment.LOW_ENGAGEMENT]: 0,
                    [CustomerSegment.DORMANT]: 0,
                    [CustomerSegment.GENERAL]: 0
                },
                timeSeriesData: []
            };
        }
    }
} 