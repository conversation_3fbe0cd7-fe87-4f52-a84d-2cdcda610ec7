
import { useState, useCallback, useEffect } from 'react'
import { json, Outlet, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import { createAddon, createAddonGroup, createVariation, deleteAddon, deleteAddonGroup, deleteVariation, getAddons, getAddonsGroups, getSeItems, getSellerBusinessConfig, getsmDistrictsAndStates, getsmSellerArea, getsmSellerConfig, getVariation, updateEditItem } from "~/services/businessConsoleService";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Pencil } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { decodePolygon } from '~/utils/polyline-utils';
import { BusinessData, MasterLocalities, MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData, MyVariationData, SellerConfig, smSellerArea, StateAndDistricts, Suppliers } from '~/types/api/businessConsoleService/SellerManagement';
import { SellerUser } from '~/types/api/businessConsoleService/netWorkinfo';
import { createSellerArea, createUser, editUser, getSellerList, getSellerUser, getUserRoles, updateArea, updateAttributes, updateSellerAttributes, updateUser } from '~/services/masterItemCategories';
import { ResponsiveTable } from '~/components/ui/responsiveTable';
import CreateUser, { User } from '~/components/ui/creatUser';
import SellerConfigDetails from '~/components/ui/sellerConfigDetails';
import { Button } from '~/components/ui/button';
import { useToast } from '~/components/ui/ToastProvider';
import MapComponent from '~/components/ui/mapComponet';
import { Switch } from '~/components/ui/switch';
import BillingConfig from '~/components/ui/billingConfi';
import SpinnerLoader from '~/components/loader/SpinnerLoader';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { SellerItem } from '~/types/api/businessConsoleService/MyItemList';
import ResponsivePagination from '~/components/ui/responsivePagination';
import EditModal from '~/components/common/EditModal';
import MyAddonsGroupTab from '~/components/common/AddonsGroupTab';
import MyAddonsTab from '~/components/common/AddonsTab';
import VariationTab from '~/components/common/VariationTab';
import { useDebounce } from '~/hooks/useDebounce';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { apiRequest } from '~/utils/api';
interface LoaderData {
  googleMapsApiKey: string
  sellerId: number,
  sellerName: string,
  activeTab: string,
  sellerAreas?: smSellerArea[],
  sellerConfig?: SellerConfig,
  url: string,
  sellerUser: SellerUser[],
  roles: { value: string; label: string }[],
  sellerBId: number;
  businessConfig: BusinessData,
  sellerItems: SellerItem[],
  statesAndDistricts: StateAndDistricts[],
  userId: number;
  permission: string[];
  addonGroupList: MyAddonGroupData,
  addonsList: MyAddonData,
  variationList: MyVariationData,
  currentPage: number;

}


export interface ActionData {
  sucess?: boolean,
  error?: string
}


export const getPolygonColor = (index: number) => {
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#f97316']
  return colors[index % colors.length]
}
export const loader = withAuth(async ({ request, user }) => {
  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''
  const url = new URL(request.url);
  const sellerId = Number(url.searchParams.get("sellerId"));
  const sellerName = (url.searchParams.get("sellerName"));
  const sellerBId = Number(url.searchParams.get("sellerBId"));
  const userId = user.userId;
  const permission = user.userDetails.roles;
  const activeTab = url.searchParams.get("activeTab") || 'SellerConfig';
  let sellerAreas: smSellerArea[] | [] = [];
  let sellerConfig: SellerConfig | {} = {};
  let sellerUser: SellerUser[] | [] = []
  let businessConfig: BusinessData | {} = {};
  let sellerItems: SellerItem[] | [] = [];
  let addonGroupList: MyAddonGroupData[] | [] = [];
  let addonsList: MyAddonData[] | [] = [];
  let variationList: MyVariationData[] | [] = [];
  let statesAndDistricts: StateAndDistricts[] | [] = [];
  const page = parseInt(url.searchParams.get("page") || "0");
  const pageSize = parseInt(url.searchParams.get("pageSize") || "50");
  const matchBy = url.searchParams.get("matchBy") || "";
  let response;
  try {
    switch (activeTab) {
      case "SellerAreas":
        const [areasResponse, districtsAndStatesResponse] = await Promise.all([getsmSellerArea(sellerId, request),
        getsmDistrictsAndStates(user.userId, request)]);
        sellerAreas = areasResponse?.data || [];
        statesAndDistricts = districtsAndStatesResponse?.data || [];
        break;
      case "SellerConfig":
        response = await getsmSellerConfig(sellerId, request);
        sellerConfig = response?.data || {};

        break;
      case "SellerUser":
        response = await getSellerUser(sellerId, request);
        sellerUser = response?.data || [];
        break;
      case "BillingConfig":
        response = await getSellerBusinessConfig("billingConfig", sellerId, request)
        businessConfig = response?.data || {}
        break;
      case "SellerItems":
        response = await getSeItems(sellerId, page, pageSize, matchBy, request)
        sellerItems = response?.data || []
        break;
      case "MyAddonsGroup":
        response = await getAddonsGroups(sellerId, page, pageSize, matchBy, request)
        addonGroupList = response?.data || []
        break;
      case "MyAddons":
        response = await getAddons(sellerId, page, pageSize, matchBy, request)
        addonsList = response?.data || []
        break;
      case "MyVariation":
        response = await getVariation(sellerId, page, pageSize, matchBy, request)
        variationList = response?.data || []
        break;
    }
    const roleResponse = await getUserRoles(request);
    const roleData = roleResponse.data as string[];
    // Mapping fetched roles to the correct labels
    const roleLabels: Record<string, string> = {
      SellerOwner: "Owner",
      DriverFull: "Driver",
      AgentFull: "Sales Agent",
      SellerManager: "Manager",
      SellerSupervisor: "Supervisor",
      AdvancedBuyer: "Buyer",
      PickerFull: "Warehouse Helper/Picker",
      ContractPriceFull: "ContractPriceFull",
      NetworkManager: "NetworkManager(mNET)",
      MnetManager: "MnetManager(mNET)",
      MnetAdmin: "MnetAdmin(mNET)",
      SalesManager: "SalesManager(mNET)",
      MnetAgent: "Agent(mNET)",
      WhatsappFull: "WhatsappFull(mNET)",
      FmSalesManager: "FmSalesManager",
      SC_Basic: "SellerBasic",
      OC_Manager: "OperationManager",
      AC_Basic: "AccountManager",
      SupplierBasic: 'SupplierBasic'

    };
    // Transform roleData into an array of `{ value, label }` objects
    const roles = roleData
      .filter((role) => roleLabels[role]) // Filter only the valid roles
      .map((role) => ({ value: role, label: roleLabels[role] }));
    return withResponse({
      googleMapsApiKey,
      sellerId,
      sellerName,
      activeTab,
      sellerAreas,
      sellerConfig,
      url,
      sellerUser,
      roles,
      sellerBId,
      businessConfig,
      sellerItems,
      statesAndDistricts,
      userId,
      permission,
      addonGroupList,
      addonsList,
      variationList,
      page,
    }, response?.headers);
  } catch (error) {
    console.log("loader failed");
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    return [];
  }
});
export const action = withAuth(async ({ request }) => {

  const formData = await request.formData();
  const intent = formData.get("intent");
  const attribute = formData.get("attribute") as string;

  const sellerId = Number(formData.get("sellerId"));
  const itemId = Number(formData.get("itemId"));

  const areaId = Number(formData.get("areaId"));
  const type = formData.get("updateType") as string;
  const updateValue = formData.get("value");
  const userId = Number(formData.get("userId"));
  const actionType = formData.get("actionType");
  if (actionType === "addonGroupAdd") {

    const mode = formData.get("mode");
    const addonGId = Number(formData.get("AddonGId"));
    const addonGroupData = formData.get("addonGroupData");
    if (!addonGroupData || typeof addonGroupData !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let addonGroup;

    try {
      addonGroup = JSON.parse(addonGroupData);
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }
    const payload = {
      id: addonGId,
      internalName: addonGroup?.internalName,
      displayName: addonGroup?.displayName,
      active: addonGroup?.active,
      description: addonGroup?.description

    }
    const finalpayload = mode === "editMode" ? payload : addonGroup;
    try {
      const response = await createAddonGroup(sellerId, finalpayload, request);
      return withResponse({ sucess: response.statusCode === 200 }, response.headers);
    }
    catch (error) {
      return new Response("Error while Updating SellerItem ", { status: 500 })
    }
  }
  if (actionType === "addonsAdd") {
    const addonData = formData.get("addonData");
    if (!addonData || typeof addonData !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let addons;
    try {
      addons = JSON.parse(addonData);

      console.log("creating.............")
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }
    try {
      const response = await createAddon(sellerId, addons, request);
      return withResponse({ sucess: response.statusCode === 200 ? true : false }, response.headers);
    }
    catch (error) {
      return new Response("Error while Updating SellerItem ", { status: 500 })
    }
  }
  if (actionType === "variationAdd") {
    const varData = formData.get("VariationData");
    const isCreating = formData.get("isCreating") as unknown as Boolean;

    if (!varData || typeof varData !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let variation;
    try {
      variation = JSON.parse(varData);
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }
    try {
      const response = await createVariation(sellerId, variation, request);
      if (response.statusCode)
        return withResponse({ sucess: response.statusCode === 200 }, response.headers);
    }
    catch (error) {
      return new Response("Error while creating Variations ", { status: 500 })
    }
  }
  if (actionType === "addonsgroupDelete") {
    const addonGId = formData.get("addonGId");
    if (!addonGId || isNaN(Number(addonGId))) {
      return new Response("Invalid or missing addonId", { status: 400 });
    }
    try {
      const response = await deleteAddonGroup(sellerId, Number(addonGId), request);
      if (response.statusCode === 200) {
        return json({ message: "Successfully deleted" }, { headers: response.headers });
      } else if (response.statusCode === 400) {
        return new Response("Bad request: Invalid addon ID or seller ID", { status: 400 });
      } else {
        return new Response(`Unexpected response status: ${response.statusCode}`, { status: response.statusCode });
      }
    } catch (error) {
      return new Response(`Error while deleting addon: ${error.message}`, { status: 500 });
    }
  }
  if (actionType === "addonsdelete") {
    const addonId = formData.get("addonId");
    if (!addonId || isNaN(Number(addonId))) {
      return new Response("Invalid or missing addonId", { status: 400 });
    }
    try {
      const response = await deleteAddon(sellerId, Number(addonId), request);
      if (response.statusCode === 200) {
        return json({ message: "Successfully deleted" }, { headers: response.headers });
      } else if (response.statusCode === 400) {
        return new Response("Bad request: Invalid addon ID or seller ID", { status: 400 });
      } else {
        return new Response(`Unexpected response status: ${response.statusCode}`, { status: response.statusCode });
      }
    } catch (error) {
      return new Response(`Error while deleting addon: ${error.message}`, { status: 500 });
    }
  }
  if (actionType === "vardelete") {
    console.log("deleteCalled..............")
    const varId = formData.get("varId");

    // Validate addonId
    if (!varId || isNaN(Number(varId))) {
      return new Response("Invalid or missing varId", { status: 400 });
    }
    try {
      const response = await deleteVariation(sellerId, Number(varId), request);
      if (response?.statusCode === 200) {
        return json({ message: "Successfully deleted" }, { headers: response.headers });
      } else if (response?.statusCode === 400) {
        return new Response("Bad request: Invalid addon ID or seller ID", { status: 400 });
      } else {
        return new Response(`Unexpected response status: ${response?.statusCode}`, { status: response.statusCode });
      }
    } catch (error) {
      return new Response(`Error while deleting addon: ${error?.message}`, { status: 500 });
    }
  }
  if (intent === "updateUser") {
    const response = await updateUser(userId, request);
    return withResponse({ intent, sellerUser: response.data, userId }, response.headers);
  }
  if (intent === "updateArea") {
    const response = await updateArea(areaId, request);
    return withResponse({ intent, sellerAreas: response.data, sellerId }, response.headers);
  }
  if (intent === "updateBillingConfig") {
    const response = await updateSellerAttributes(type, sellerId, attribute, updateValue, request);
    return withResponse(
      {
        intent: intent,
        businessConfig: response.data,
        sellerId: sellerId,
      },
      response.headers
    );
  }
  if (intent === "createSellerArea") {
    try {
      console.log("📩 Received formData:", Object.fromEntries(formData));
      console.log("✅ Call `addNetworkLocalities`");

      const newLocalities = JSON.parse(formData.get("areaIds") as string || "[]");

      console.log(`newLocalities : `, newLocalities);

      if (!Array.isArray(newLocalities) || newLocalities.length === 0) {
        console.error("❌ Invalid request payload");
        return json({ error: "Invalid request payload" }, { status: 400 });
      }

      // Extract only `id` values from `newLocalities`
      const areaIds = newLocalities.map((locality) => locality.id);

      console.log("Extracted areaIds:", areaIds);

      // Map over the extracted IDs and create an array of promises
      const apiPromises = areaIds.map(async (areaId) => {
        try {
          console.log(`📡 Sending API request for areaId: ${areaId}`);
          return await createSellerArea(sellerId, areaId, request);
        }
        catch (error) {
          console.error(`❌ Error in API call for areaId ${areaId}:`, error);
          return { error: `Failed for areaId ${areaId}` };
        }

      });

      // Wait for all API calls to complete
      const apiResponses = await Promise.all(apiPromises);

      console.log("✅ All API Responses:", apiResponses);
      return json({ success: true, data: apiResponses });
    }
    catch (error) {
      console.error(`❌ Error in API call for agentUserId ${areaId}:`, error);
      return json({ error: "Failed to createArea" }, { status: 500 });
    }
  }
  // const userDataString = formData.get("userData");
  if (intent === "editSellerItem") {
    const sellerItemData = formData.get("sellerItem");
    if (!sellerItemData || typeof sellerItemData !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let sellerData;
    try {
      sellerData = JSON.parse(sellerItemData);
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }

    try {
      const response = await updateEditItem(sellerId, itemId, sellerData, request);
      return withResponse({ intent, sellerItems: response.data, sellerId, itemId }, response.headers);

    }
    catch (error) {
      return new Response("Error while Updating SellerItem ", { status: 500 })
    }
  }

  if (intent === "editUser") {
    const userDataString = formData.get("userData");
    if (!userDataString || typeof userDataString !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let userData;
    try {
      userData = JSON.parse(userDataString);
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }
    if (!Array.isArray(userData.roles) || userData.roles.length === 0) {
      return json({ error: "Roles must be an array with at least one value" }, { status: 400 });
    }
    const response = await editUser(userId, userData, request);
    return withResponse({ intent, sellerUser: response.data, userId }, response.headers);
  }
  if (intent === "createUser") {
    const userDataString = formData.get("userData");
    if (!userDataString || typeof userDataString !== "string") {
      return json({ error: "Invalid request payload" }, { status: 400 });
    }
    let userData;
    try {
      userData = JSON.parse(userDataString);
      console.log(userData, "User data parsed");
    } catch (error) {
      return json({ error: "Invalid JSON format" }, { status: 400 });
    }
    if (!Array.isArray(userData.roles) || userData.roles.length === 0) {
      return json({ error: "Roles must be an array with at least one value" }, { status: 400 });
    }
    const response = await createUser(sellerId, userData, request);
    return withResponse({ intent, SellerConfig: response.data, sellerId }, response.headers);
  }
  if (type === "seller") {
    // Supported attributes as expected by your backend.
    const supportedAttributes = [
      "autoAccept", "autoPack", "autoPickup", "autoDispatch", "approxPricing",
      "strikeoffEnabled", "itemPickEnabled", "contractPriceEnabled", "isPayLaterEnabled",
      "waEnable", "autoActivate", "allowCoD", "favItemsEnabled", "name", "minimumRequiredBalance",
      "deliveryTime", "approxDelDateVisibility", "instantDeliveryTime", "approxPriceVisibility", "minimumOrderValue", "categoryLevel", "bookingCloseTime", "bookingOpenTime",
      "dispatchTime", "deliveryType", "minimumOrderQty", "listingSequence", "advanceBookingDays", "t1Open",
      "t1Close", "t2Open", "t2Close", "t3Open", "t3Close", "menuId", "pos", "posCustId", "posRetName",
      "posRetContactNo", "posRetAddress", "outletId", "ondcDomain", "defaultOrderPrepTime", "spId", "sourceSystem", "miSources", "logisticProvider", "distanceBasedDel", "deliveryDistance",
      "packagingCharge", "packagingChargeType", "packagingApplicableOn","platformFee","platformFeePerc","platformFeePkg"
    ];
    // Retrieve the "updates" field from the FormData.
    const updatesField = formData.get("updates");
    console.log("updatesField", updatesField);
    if (!updatesField) {
      return json({ error: "Missing updates" }, { status: 400 });
    }
    // First, parse the JSON string from the updates field.
    let parsedUpdates: any;
    try {
      parsedUpdates = JSON.parse(updatesField as string);
    } catch (error) {
      return json({ error: "Invalid JSON format in updates" }, { status: 400 });
    }
    console.log("parsedUpdates", parsedUpdates);

    // Check if we directly got an array.
    if (Array.isArray(parsedUpdates)) {
      // Do nothing, we already have an array.
    }
    // If it's an object with a property "updates", try to parse that.
    else if (parsedUpdates && typeof parsedUpdates === "object" && parsedUpdates.updates) {
      if (typeof parsedUpdates.updates === "string") {
        try {
          parsedUpdates = JSON.parse(parsedUpdates.updates);
        } catch (error) {
          return json({ error: "Invalid JSON format in nested updates" }, { status: 400 });
        }
      } else {
        parsedUpdates = parsedUpdates.updates;
      }
    } else {
      // Not an array and not an object with an "updates" property.
      return json({ error: "Updates must be an array" }, { status: 400 });
    }

    // Final check: Ensure parsedUpdates is now an array.
    if (!Array.isArray(parsedUpdates)) {
      return json({ error: "Updates must be an array" }, { status: 400 });
    }

    console.log("Parsed updates:", parsedUpdates);

    // separate pos updates from parsedUpdates
    if (parsedUpdates.find(item => item.attribute === "pos")?.value === "petpooja") {
      let requestBody = {
        menuId: parsedUpdates.find(item => item.attribute === "menuId")?.value,
        posCustId: parsedUpdates.find(item => item.attribute === "posCustId")?.value,
        posRetName: parsedUpdates.find(item => item.attribute === "posRetName")?.value,
        posRetContactNo: parsedUpdates.find(item => item.attribute === "posRetContactNo")?.value,
        posRetAddress: parsedUpdates.find(item => item.attribute === "posRetAddress")?.value,
      };
      parsedUpdates = parsedUpdates.filter(
        item => !["pos", "menuId", "posCustId", "posRetName", "posRetContactNo", "posRetAddress"].includes(item.attribute)
      );
      try {
        const response = await apiRequest<SellerConfig>(
          `${process.env.API_BASE_URL}/bc/mnetadmin/config/${type}/${sellerId}/attr/pos/petpooja`,
          "POST",
          requestBody,
          {},
          true,
          request
        );
      }
      catch (error) {
        console.log("Error while updating POS PETPOOJA:", error);
      }
    }

    // separate packaging updates from parsedUpdates
    if (parsedUpdates.find(item => item.attribute === "packagingCharge")?.value) {
      let requestBody = {
        packagingCharge: parsedUpdates.find(item => item.attribute === "packagingCharge")?.value,
        packagingChargeType: parsedUpdates.find(item => item.attribute === "packagingChargeType")?.value,
        packagingApplicableOn: parsedUpdates.find(item => item.attribute === "packagingApplicableOn")?.value,
      };
      parsedUpdates = parsedUpdates.filter(
        item => !["packagingCharge", "packagingChargeType", "packagingApplicableOn"].includes(item.attribute)
      );
      try {
        const response = await apiRequest<SellerConfig>(
          `${process.env.API_BASE_URL}/bc/mnetadmin/config/${type}/${sellerId}/attr/packagingCharge/packagingCharge`,
          "POST",
          requestBody,
          {},
          true,
          request
        );
      }
      catch (error) {
        console.log("Error while updating packaging:", error);
      }
    }

    let sellerConfigUpdated: any = {};
    // Process each update.
    for (const { attribute, value } of parsedUpdates) {
      if (!supportedAttributes.includes(attribute)) {
        console.log(`Skipping unsupported attribute: ${attribute}`);
        continue;
      }
      console.log("Action: Updating seller detail:", { attribute, value });
      const updatedResponse = await updateAttributes(type, sellerId, attribute, value, request);
      console.log(`Action: Updated ${attribute} with value ${value}`);
      sellerConfigUpdated = { ...sellerConfigUpdated, ...updatedResponse.data };
    }
    console.log("Action: All update API calls processed.");
    return withResponse({ sellerConfig: sellerConfigUpdated, sellerId }, new Headers());
  }
  return json({ error: "Invalid intent or updateType" }, { status: 400 });
});
export default function SellerDetailsPage() {
  const { googleMapsApiKey, sellerId, sellerName, activeTab, sellerAreas, sellerConfig, url, sellerUser, roles, sellerBId, businessConfig, sellerItems, statesAndDistricts, userId, permission, currentPage, addonGroupList, addonsList, variationList } = useLoaderData<LoaderData>()
  // If no networks, show a "No results" row
  const dataleng = sellerAreas ? sellerAreas.length : ""
  const fetchfor = useNavigate();
  const adminBasic = permission && Array.isArray(permission) ? permission.includes("AC_Basic") : false;

  const handleTabChange = (newTab: string) => {
    if (newTab === "SellerCategories") {
      // Navigate to the nested route for SellerCategories
      fetchfor(`/home/<USER>/sellerCategories?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`);
    }
    else if (newTab === "DeliveryConfig") {
      fetchfor(`/home/<USER>/deliveryconfig?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`)
    }
    else {
      // For other tabs, stay on the main route
      fetchfor(`/home/<USER>
    }
  };
  const { showToast } = useToast()
  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set())
  const [isLoading, setIsLoading] = useState(true); // Controls loader visibility
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpenEdit, setIsModalOpenEdit] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const onLoad = useCallback((mapInstance: google.maps.Map) => {
    setMap(mapInstance)
    setMapLoaded(true)
  }, [])
  interface pointerLocation {
    latitude: number | null;
    longitude: number | null;
  }
  const [isLocateShopClicked, setisLocateShopClicked] = useState(false);
  const [pointerLocation, setPointerLocation] = useState<pointerLocation>({
    latitude: null,
    longitude: null
  })
  const fetcher = useFetcher()
  const handleLocateShopClicked = useCallback((state: boolean) => { setisLocateShopClicked(state); }, []
  );

  useEffect(() => {
    if (map && sellerAreas) {
      const decodedPolygons = sellerAreas.map((area) => ({
        id: area.sellerAreaId,
        paths: area.area.polygon ? decodePolygon(area.area.polygon) : [],
      }));
      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id))); // Track which polygons to show
    }
  }, [map, sellerAreas]);

  useEffect(() => {
    // Set loading to false only when both API data and map are ready
    if (googleMapsApiKey && sellerAreas) {
      if (mapLoaded) {
        setIsLoading(false); // Everything is ready
      }
    }
  }, [googleMapsApiKey, sellerAreas, mapLoaded]);

  const onUnmount = useCallback(() => {
    setMap(null)
    setMapLoaded(false)
  }, [])
  const getPolygonCenter = (paths: google.maps.LatLngLiteral[]) => {
    const latitudes = paths.map((path) => path.lat);
    const longitudes = paths.map((path) => path.lng);

    const latSum = latitudes.reduce((a, b) => a + b, 0);
    const lngSum = longitudes.reduce((a, b) => a + b, 0);
    return {
      lat: latSum / latitudes.length,
      lng: lngSum / longitudes.length,
    };
  };
  const UserHeaders = [
    "Id",
    "Name",
    "Business Name",
    "Mobile Number",
    "Cash With User",
    "Roles",
    "Status",
    ""
  ];
  const sellerItemHeader = [
    "Name",
    "WtFactor",
    "MaxAvQty",
    "MinMax",
    "Inc-MaxOrd",
    "Price",
    "Supp",
    "Distcharg(PC-PU)",
    "AcPkg(min-max)",
    "DistcPkg(min-max)",
    ""
  ];
  const navigate = useNavigate()
  const handleUpdate = async (data: Array<{ attribute: string; value: any }>) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "seller");
      formData.append("sellerId", sellerId.toString());
      // Send all updates as a single JSON string.
      formData.append("updates", JSON.stringify(data));
      console.log("handleUpdate called with data:", data);
      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }

      fetcher.submit(formData, { method: "POST" });
      console.log("handleUpdate: API call submitted");
      showToast("Seller configuration updated successfully", "success");
    } catch (error) {
      console.error("Error in handleUpdate:", error);
      showToast("Failed to update seller configuration", "error");
    }
  };
  const onSellerAttributeUpdate = async (attribute: string, value: any) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "billingConfig");
      formData.append("intent", "updateBillingConfig")
      formData.append("sellerId", sellerId.toString());
      formData.append("attribute", attribute);
      formData.append("value", value.toString());
      await fetcher.submit(formData, { method: "POST" });

      showToast(`${attribute.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
      // Update UI only after successful response
    } catch (error) {
      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, " $1")}`, "error");
    }
  };
  const [toggleUserStatus, setToggleUserStatus] = useState<Record<number, boolean>>(() =>

    sellerUser?.reduce((acc, user) => {
      acc[user?.userId] = user?.disabled;
      return acc;

    }, {} as Record<number, boolean>)

  );
  const updateToggleUser = (userId: number) => {
    const formData = new FormData()
    formData.append("userId", userId as unknown as string)
    formData.append("intent", "updateUser")
    fetcher.submit(formData, { method: "put" })
    if (fetcher.state === "idle") {
      showToast("User Status Updated Success ", "success")
    }
  }
  const handleSwitchUser = async (userId: number) => {
    // Optimistically toggle the switch
    setToggleUserStatus((prev) => ({
      ...prev,
      [userId]: !prev[userId],
    }))

    updateToggleUser(userId)
  }
  const updateToggle = (areaId: number) => {
    const formData = new FormData()
    formData.append("sellerId", sellerId as unknown as string)
    formData.append("areaId", areaId as unknown as string)
    formData.append("intent", "updateArea")
    fetcher.submit(formData, { method: "POST" })
  }
  const [user, setUser] = useState<User | undefined>()
  const handleEdit = (row: any) => {
    setUser(row)
    setIsModalOpenEdit(true);
  }
  const handleCreateAreas = (areaIds: MasterLocalities[]) => {
    const formData = new FormData();
    formData.append("intent", "createSellerArea");
    formData.append("areaIds", JSON.stringify(areaIds)); // Properly stringify the array
    formData.append("sellerId", sellerId.toString());

    fetcher.submit(formData, { method: "post" });
  };
  const loading = fetcher.state !== "idle";
  const [searchTerm, setSearchTerm] = useState('')
  const [pageSize, setPageSize] = useState("50");
  const [pageNum, setPageNum] = useState(0)

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(newPageSize)

    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`)
  }
  const debounceSearchTerm = useDebounce(searchTerm, 300);
  const handlePageSearch = (value: string) => {
    setSearchTerm(value);

  }
  useEffect(() => {
    if (debounceSearchTerm.length >= 3) {
      // Perform search when the input has 3 or more characters
      navigate(
        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`
      );
    } else {
      // Reset search when input is less than 3 characters
      navigate(
        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`
      );
    }

  }, [debounceSearchTerm])

  const handlePageChange = (newPageSize: string) => {
    setPageNum(Number(newPageSize))
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`)
  }
  const [selectedItem, setSelectedItem] = useState({});
  const [isItemModalOpen, setIsItemModalOpen] = useState(false);
  const [supplierList, setSupplierList] = useState<Suppliers[]>([]);
  const handleEditModal = useCallback(async (row: SellerItem) => {
    try {
      const response = await fetch(`./api/supplierItems?itemId=${row.Id}`)

      if (!response.ok) {
        throw new Error("Failed to fetch item details");
      }

      const supplier = await response.json();
      setSupplierList(supplier?.supplierData?.data)
      setSelectedItem(row);
      setIsItemModalOpen(true);
    } catch (error) {
      console.error("Error fetching item:", error);
    }
  }, [])
  const handleSave = (updatedData: any) => {
    console.log("Updated Data:", updatedData);
    const formData = new FormData()
    formData.append("intent", "editSellerItem");
    formData.append("sellerItem", JSON.stringify(updatedData));
    formData.append("itemId", updatedData.Id);
    formData.append("sellerId", sellerId.toString());
    fetcher.submit(formData, { method: "put" })
    setIsItemModalOpen(false);
  };


  return (
    <div className="h-full">
      {loading && (
        <SpinnerLoader loading={loading} />
      )}
      <h1 className=" mb-4 font-bold cursor-pointer" onClick={() => navigate("/home/<USER>")}> <span className="text-2xl">Seller Management / </span> <span className="text-xl">{sellerName} </span> </h1>
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="SellerConfig">Configurations</TabsTrigger>
          <TabsTrigger value="SellerAreas">Areas</TabsTrigger>
          <TabsTrigger value="SellerUser">Users</TabsTrigger>
          <TabsTrigger value="SellerItems">SellerItems</TabsTrigger>
          <TabsTrigger value="SellerCategories">Seller Categories</TabsTrigger>
          <TabsTrigger value="MyAddonsGroup">My AddonsGroups</TabsTrigger>
          <TabsTrigger value="MyAddons">My Addons</TabsTrigger>
          <TabsTrigger value="MyVariation">My Variations</TabsTrigger>
          <TabsTrigger value="DeliveryConfig">DeliveryConfig</TabsTrigger>
          {adminBasic && <TabsTrigger value="BillingConfig">Bank Details</TabsTrigger>}
        </TabsList>
        <Outlet />
        <TabsContent value="MyAddonsGroup">
          <MyAddonsGroupTab
            addonGroupTabData={Array.isArray(addonGroupList) ? addonGroupList : addonGroupList ? [addonGroupList] : []}
            sellerId={sellerId}

          />
        </TabsContent>
        <TabsContent value="MyAddons">
          <MyAddonsTab
            addonsList={Array.isArray(addonsList) ? addonsList : addonsList ? [addonsList] : []}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            pageSize={pageSize}
            setPageSize={setPageSize}
            pageNum={pageNum}
            setPageNum={setPageNum}
            isItemModalOpen={isItemModalOpen}
            setIsItemModalOpen={setIsItemModalOpen}
            selectedItem={selectedItem}
            setSelectedItem={setSelectedItem}
            sellerId={sellerId}
          />
        </TabsContent>
        <TabsContent value="MyVariation">
          <VariationTab
            variationData={Array.isArray(variationList) ? variationList : variationList ? [variationList] : []}
            sellerId={sellerId}
          />
        </TabsContent>
        <TabsContent value="SellerItems">
          <div className="flex justify-between mb-4">
            <Input
              placeholder="Search by Item Name"
              value={searchTerm}
              type='search'
              onChange={(e) => handlePageSearch(e.target.value)}
              className="max-w-sm  rounded-full"
            />

            <Select value={pageSize} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Items per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 per page</SelectItem>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="20">20 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <ResponsiveTable
            headers={sellerItemHeader}
            data={
              sellerItems
            }
            renderRow={(row) => {
              return (
                <tr
                  key={row.Id}
                  className={`border-b transition-all duration-200 ${row.freeItem
                    ? 'bg-[linear-gradient(88deg,#F0FFF0_0%,#FFF_100%)]'
                    : 'hover:bg-gray-100 hover:shadow-sm'
                    } rounded-lg`}
                >
                  <td
                    className="py-2 cursor-pointer "
                    onClick={() =>
                      navigate(`/home/<USER>
                    }
                  >
                    <div className="flex items-center gap-1 justify-start p-1 rounded-lg bg-white/50 shadow-sm hover:shadow-md transition-shadow duration-150 w-fit">
                      <img
                        src={row?.picture}
                        alt={row?.name}
                        className="h-12 w-12 rounded-full object-cover flex-shrink-0 border border-gray-200"
                      />
                      <div className="flex flex-col items-start text-left">
                        <span className="text-sm font-medium text-blue-600 hover:underline max-w-32 break-words">
                          {row?.name} {row?.unit}
                        </span>
                        {row?.freeItem && (
                          <span className="text-xs font-semibold text-teal-500 bg-teal-100 px-2 py-1 rounded-full mt-1">
                            Free
                          </span>
                        )}
                      </div>
                    </div>

                    {row.description && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="mt-2 flex justify-center">
                              <span className="text-xs font-semibold text-orange-500 hover:text-orange-600 cursor-pointer underline">
                                View Description
                              </span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="bg-gray-800 text-white p-3 rounded-md max-w-xs">
                            <p className="text-sm">Description: {row?.description || "-"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </td>
                  <td className="py-2 px-2 text-center  whitespace-normal break-words"
                  >
                    {row?.weightFactor}
                  </td>
                  <td className="py-2 px-2 text-center  whitespace-normal break-words"
                  >
                    {row?.maxAvailableQty}
                  </td>
                  <td className="py-2 px-2 text-center  whitespace-normal break-words"
                  >
                    {row?.minOrderQty}-{row?.maxOrderQty}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.maxOrderQty}-{row?.incrementOrderQty}
                  </td>

                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.pricePerUnit}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.supplierName}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.distChargePc}-{row?.distChargePu}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.minAcPkg}-{row?.maxAcPkg}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.minDistcPkg}-{row?.maxDistcPkg}
                  </td>
                  <td className="py-2 px-3 text-center cursor-pointer">
                    <Pencil onClick={() => handleEditModal(row)} />
                  </td>
                </tr>
              )
            }}
          />
          <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
            <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
            <div className="overflow-x-auto">
              <ResponsivePagination
                totalPages={Number(pageSize)}
                currentPage={pageNum}
                onPageChange={(pageNum) => handlePageChange(pageNum.toString())}
              />
            </div>
          </div>
          {/* edit modal flexible for both create and edit seller item*/}
          <EditModal

            isOpen={isItemModalOpen}
            data={selectedItem || {}}
            onClose={() => {
              setIsItemModalOpen(false)
              setSelectedItem({});
            }}
            onSave={handleSave} multiple={false} supplierList={supplierList} />
          {/* <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => setIsModalOpen(true)}>+ Add SellerItem</Button> */}
        </TabsContent>
        <TabsContent value="SellerUser">
          <ResponsiveTable
            headers={UserHeaders}
            data={
              sellerUser
            }
            renderRow={(row) => {
              return (
                <tr key={row.userId} className="border-b">
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >{row.userId}</td>
                  <td className="py-2 px-3 text-center whitespace-normal break-words"
                  >
                    {row?.userName}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.businessName}
                  </td>
                  <td className="py-2 px-3 text-center whitespace-normal break-words"
                  >
                    {row?.mobileNumber}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.cashWithUser}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    {row?.roles?.map(x => <div> {x === "SellerOwner" ? "Owner" : x === "DriverFull" ? "Driver" : x === "SupplierBasic" ? "SupplierBasic" : x === "AgentFull" ? "Agent" : x === "SellerManager" ? "Manager" : x === "SellerSupervisor" ?
                      "Supervisor" : x === "PickerFull" ? " Warehouse Helper/ Picker" : x === "AdvancedBuyer" ? "Buyer" :
                        x === "ContractPriceFull" ? "ContractPrice" : x === "NetworkManager" ? "NetworkManager(mNET)" : x === "MnetManager" ? "MnetManager(mNET)" : x === "MnetAdmin" ? "MnetAdmin(mNET)" : x === "SalesManager" ? "SalesManager(mNET)" : x === "WhatsappFull" ? "WhatsappFull(mNET)" : x === "MnetAgent" ? "Agent(mNET)" :
                          x === "SC_Basic" ? "SellerBasic" : x === "OC_Manager" ? "OperationManager" : x === "AC_Basic" ? "AccountManager" : x === "FmSalesManager" ? "FmSalesManager" : ""}</div>)}

                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words"
                  >
                    <Switch
                      checked={!toggleUserStatus[row.userId]}
                      onClick={() => handleSwitchUser(row.userId)}

                    />
                  </td>
                  <td className="py-2 px-3 text-center">
                    <Pencil onClick={() => handleEdit(row)} />
                  </td>
                </tr>
              )
            }}
          />
          <CreateUser isOpen={isModalOpenEdit} onClose={() => setIsModalOpenEdit(false)} sellerId={sellerId}
            roles={roles ? roles : []} sellerBId={sellerBId} user={user}
          />
          <CreateUser isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} sellerId={sellerId}
            roles={roles ? roles : []} sellerBId={sellerBId}
          />
          <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => setIsModalOpen(true)}>+ Add User</Button>

        </TabsContent>
        <TabsContent value={"BillingConfig"}>
          <BillingConfig billingConfig={businessConfig} onSellerAttributeUpdate={onSellerAttributeUpdate} />

        </TabsContent>
      </Tabs>
      {activeTab === "SellerConfig" && (sellerConfig
        ? (<SellerConfigDetails sellerConfig={sellerConfig} onAttributeUpdate={handleUpdate} />
        ) : (<div className="flex items-center justify-center h-full">
          <p className="text-red-500">Unable to find network configurations.</p>
        </div>))}
      {activeTab === "SellerAreas" && (sellerAreas ? (
        <MapComponent googleMapsApiKey={googleMapsApiKey} sellerAreas={sellerAreas} visibleAreas={visibleAreas}
          decodePolygon={decodePolygon}
          getPolygonCenter={getPolygonCenter}
          getPolygonColor={getPolygonColor}
          handleLocateShopClicked={handleLocateShopClicked}
          updateToggle={updateToggle}
          onLoad={onLoad} onUnmount={onUnmount} isLocateShopClicked={isLocateShopClicked}
          statesAndDistricts={statesAndDistricts}
          userId={userId}
          sellerId={sellerId}
          handleSubmit={(areaIds: MasterLocalities[]) => handleCreateAreas(areaIds)}
        />
      )
        : (<div className="flex items-center justify-center h-full">
          <p className="text-red-500">Unable to find sellerAreas .</p>
        </div>)
      )
      }
    </div >
  );

}
