import { NotificationLogRepository } from '../database/repository/NotificationLogRepository.js';
import { NotificationChannel, NotificationStatus, NotificationLog, CampaignType, MessageCategory, CustomerSegment } from '../database/entities/NotificationLog.js';
import { 
    CampaignNotificationRequest,
    CampaignAnalytics,
    CustomerEngagementAnalytics,
    NotificationAnalyticsSummary,
    WebhookNotificationUpdate,
    NotificationSyncResult,
    NotificationQueryFilters
} from '../types/notification.types.js';
import { formatInTimeZone } from 'date-fns-tz';

export class NotificationLogService {
    private repository: NotificationLogRepository;

    constructor() {
        this.repository = new NotificationLogRepository();
    }

    async logWhatsAppNotification(
        businessId: string,
        mobileNumber: string,
        recipient: string,
        inputPayload: any,
        providerRequest: any,
        providerResponse: any,
        status: NotificationStatus,
        errorMessage?: string
    ): Promise<NotificationLog> {
        const logData: any = {
            businessId,
            mobileNumber,
            channel: NotificationChannel.WHATSAPP,
            recipient,
            inputPayload,
            providerRequest,
            providerResponse,
            status,
            retryCount: 0,
            lastUpdated: Date.now()
        };

        // Add errorMessage only if it has a value
        if (errorMessage !== undefined) {
            logData.errorMessage = errorMessage;
        }

        // 🆕 Comprehensively remove all undefined values
        const cleanedLogData = this.removeUndefinedValues(logData);
        
        return this.repository.createLog(cleanedLogData);
    }

    async logFirebaseNotification(
        businessId: string,
        mobileNumber: string,
        recipient: string,
        inputPayload: any,
        providerRequest: any,
        providerResponse: any,
        status: NotificationStatus,
        errorMessage?: string
    ): Promise<NotificationLog> {
        const logData: any = {
            businessId,
            mobileNumber,
            channel: NotificationChannel.FIREBASE,
            recipient,
            inputPayload,
            providerRequest,
            providerResponse,
            status,
            retryCount: 0,
            lastUpdated: Date.now()
        };

        // Add errorMessage only if it has a value
        if (errorMessage !== undefined) {
            logData.errorMessage = errorMessage;
        }

        // 🆕 Comprehensively remove all undefined values
        const cleanedLogData = this.removeUndefinedValues(logData);
        
        return this.repository.createLog(cleanedLogData);
    }

    async updateNotificationStatus(
        notificationId: string,
        timestamp: number,
        status: NotificationStatus,
        providerResponse?: any,
        errorMessage?: string,
        whatsappMessageId?: string
    ): Promise<void> {
        const updates: Partial<NotificationLog> = {
            status
        };

        // Add optional fields only if they have values
        if (providerResponse !== undefined) {
            updates.providerResponse = providerResponse;
        }

        if (whatsappMessageId !== undefined) {
            updates.whatsappMessageId = whatsappMessageId;
        }

        if (errorMessage !== undefined) {
            updates.errorMessage = errorMessage;
        }

        const updateTime = Date.now();
        switch (status) {
            case NotificationStatus.SENT:
                updates.sentAt = updateTime;
                break;
            case NotificationStatus.DELIVERED:
                updates.deliveredAt = updateTime;
                break;
            case NotificationStatus.READ:
                updates.readAt = updateTime;
                break;
            case NotificationStatus.FAILED:
                updates.failedAt = updateTime;
                break;
        }

        // 🆕 Comprehensively remove all undefined values
        const cleanedUpdates = this.removeUndefinedValues(updates);

        await this.repository.updateLog(notificationId, timestamp, cleanedUpdates);
    }

    async incrementRetryCount(notificationId: string, timestamp: number): Promise<void> {
        const log = await this.repository.getLog(notificationId, timestamp);
        if (!log) {
            throw new Error('Notification log not found');
        }

        await this.repository.updateLog(notificationId, timestamp, {
            retryCount: log.retryCount + 1
        });
    }

    async getBusinessNotificationLogs(
        businessId: string,
        startTime?: number,
        endTime?: number
    ): Promise<NotificationLog[]> {
        return this.repository.getLogsByBusiness(businessId, startTime, endTime);
    }

    async getNotificationLog(notificationId: string, timestamp: number): Promise<NotificationLog | null> {
        return this.repository.getLog(notificationId, timestamp);
    }

    /**
     * Helper function to recursively remove undefined values from objects
     */
    private removeUndefinedValues(obj: any): any {
        if (obj === null || obj === undefined) {
            return null;
        }
        
        if (Array.isArray(obj)) {
            return obj.map(item => this.removeUndefinedValues(item)).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
            const cleaned: any = {};
            for (const [key, value] of Object.entries(obj)) {
                if (value !== undefined) {
                    cleaned[key] = this.removeUndefinedValues(value);
                }
            }
            return cleaned;
        }
        
        return obj;
    }

    async logCampaignNotification(
        campaignRequest: CampaignNotificationRequest & {
            recipient: string;
            providerRequest: any;
            providerResponse?: any;
            status: NotificationStatus;
            errorMessage?: string;
            whatsappMessageId?: string;
            webhookLogId?: string;
        }
    ): Promise<NotificationLog> {
        const timestamp = Date.now();
                
        // Start with the base WhatsApp notification structure
        // This ensures we cover everything that logWhatsAppNotification covers
        const logData: any = {
            // Base WhatsApp notification fields (same as logWhatsAppNotification)
            businessId: campaignRequest.businessId,
            mobileNumber: campaignRequest.recipient,
            channel: NotificationChannel.WHATSAPP,
            recipient: campaignRequest.recipient,
            inputPayload: {
                // Construct inputPayload from campaign request data
                businessId: campaignRequest.businessId,
                campaignId: campaignRequest.campaignId,
                campaignName: campaignRequest.campaignName,
                campaignType: campaignRequest.campaignType,
                messageCategory: campaignRequest.messageCategory,
                customerSegment: campaignRequest.customerSegment,
                recipients: campaignRequest.recipients,
                templateName: campaignRequest.templateName,
                templateValues: campaignRequest.templateValues,
                messageContent: campaignRequest.messageContent,
                scheduleAt: campaignRequest.scheduleAt,
                timezone: campaignRequest.timezone,
                trackingEnabled: campaignRequest.trackingEnabled,
                customMetadata: campaignRequest.customMetadata,
                tags: campaignRequest.tags
            },
            providerRequest: campaignRequest.providerRequest,
            providerResponse: campaignRequest.providerResponse || null,
            status: campaignRequest.status,
            retryCount: 0,
            lastUpdated: timestamp,
            
            // Campaign-specific extensions
            campaignId: campaignRequest.campaignId,
            campaignName: campaignRequest.campaignName,
            campaignType: campaignRequest.campaignType,
            messageCategory: campaignRequest.messageCategory,
            customerSegment: campaignRequest.customerSegment || CustomerSegment.GENERAL,
            whatsappStatus: campaignRequest.status
        };

        // Add optional base fields (same as logWhatsAppNotification)
        if (campaignRequest.errorMessage !== undefined) {
            logData.errorMessage = campaignRequest.errorMessage;
        }

        // Add optional campaign-specific fields
        if (campaignRequest.tags !== undefined) {
            logData.tags = campaignRequest.tags;
        }
        
        if (campaignRequest.whatsappMessageId !== undefined) {
            logData.whatsappMessageId = campaignRequest.whatsappMessageId;
        }
        
        if (campaignRequest.webhookLogId !== undefined) {
            logData.webhookLogId = campaignRequest.webhookLogId;
        }
        
        // Add sentAt timestamp if status is SENT (campaign-specific enhancement)
        if (campaignRequest.status === NotificationStatus.SENT) {
            logData.sentAt = timestamp;
        }
        
        // 🆕 Comprehensively remove all undefined values
        const cleanedLogData = this.removeUndefinedValues(logData);
                
        try {
            return await this.repository.createLog(cleanedLogData);
        } catch (error) {
            console.error('❌ Error in repository.createLog:', error);
            console.error('❌ Data that caused error:', JSON.stringify(cleanedLogData, null, 2));
            throw error;
        }
    }

    /**
     * Update notification status from webhook data
     * 🆕 Now automatically handles ISO timestamp conversion
     */
    async updateStatusFromWebhook(webhookUpdate: WebhookNotificationUpdate): Promise<NotificationSyncResult> {
        const syncId = `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();
        
        const result: NotificationSyncResult = {
            syncId,
            timestamp: startTime,
            totalUpdatesProcessed: 1,
            successfulUpdates: 0,
            failedUpdates: 0,
            skippedUpdates: 0,
            processedNotifications: [],
            failedNotifications: [],
            processingDuration: 0,
            averageUpdateTime: 0
        };

        try {
            // Find notification by WhatsApp message ID
            const notification = await this.repository.findByWhatsAppMessageId(webhookUpdate.whatsappMessageId);
            
            if (!notification) {
                result.skippedUpdates = 1;
                result.failedNotifications.push({
                    whatsappMessageId: webhookUpdate.whatsappMessageId,
                    error: 'Notification not found for WhatsApp message ID'
                });
                return result;
            }

            // Prepare updates with timestamps
            const updates: Partial<NotificationLog> = {
                whatsappStatus: webhookUpdate.status,
                status: webhookUpdate.status
            };

            // Set appropriate timestamp based on status (ISO timestamps will be auto-generated by repository)
            const updateTime = Date.now();
            switch (webhookUpdate.status) {
                case NotificationStatus.SENT:
                    updates.sentAt = updateTime;
                    // sentAtISO will be auto-generated by repository
                    break;
                case NotificationStatus.DELIVERED:
                    updates.deliveredAt = updateTime;
                    // deliveredAtISO will be auto-generated by repository
                    break;
                case NotificationStatus.READ:
                    updates.readAt = updateTime;
                    // readAtISO will be auto-generated by repository
                    break;
                case NotificationStatus.FAILED:
                    updates.failedAt = updateTime;
                    // failedAtISO will be auto-generated by repository
                    if (webhookUpdate.error) {
                        updates.errorMessage = webhookUpdate.error.message;
                    }
                    break;
            }

            // Update the notification (repository will automatically add ISO timestamps)
            const updateSuccess = await this.repository.updateByWhatsAppMessageId(
                webhookUpdate.whatsappMessageId,
                updates
            );

            if (updateSuccess) {
                result.successfulUpdates = 1;
                result.processedNotifications.push(notification.notificationId);
                console.log(`✅ Notification ${notification.notificationId} updated to ${webhookUpdate.status} at ${new Date(updateTime).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}`);
            } else {
                result.failedUpdates = 1;
                result.failedNotifications.push({
                    notificationId: notification.notificationId,
                    whatsappMessageId: webhookUpdate.whatsappMessageId,
                    error: 'Failed to update notification'
                });
            }

        } catch (error) {
            result.failedUpdates = 1;
            result.failedNotifications.push({
                whatsappMessageId: webhookUpdate.whatsappMessageId,
                error: error instanceof Error ? error.message : String(error)
            });
        }

        // Calculate performance metrics
        const endTime = Date.now();
        result.processingDuration = endTime - startTime;
        result.averageUpdateTime = result.processingDuration; // For single update

        return result;
    }

    /**
     * 🆕 Convert Unix timestamp to proper ISO 8601 format in IST timezone using date-fns-tz
     * Format: 2023-12-30T15:30:45.123+05:30
     */
    private toISTISOString(timestamp: number): string {
        return formatInTimeZone(
            new Date(timestamp),
            'Asia/Kolkata',
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
        );
    }

    async getCampaignAnalytics(campaignId: string): Promise<CampaignAnalytics> {
        try {
            const analyticsData = await this.repository.getCampaignAnalyticsData(campaignId);
            const notifications = await this.repository.getByCampaignId(campaignId);
            
            if (notifications.length === 0) {
                throw new Error(`Campaign ${campaignId} not found or has no notifications`);
            }

            const firstNotification = notifications[notifications.length - 1];
            const lastNotification = notifications[0];

            const deliveryRate = analyticsData.totalMessages > 0 
                ? (analyticsData.statusCounts[NotificationStatus.DELIVERED] / analyticsData.totalMessages) * 100 
                : 0;
            
            const readRate = analyticsData.statusCounts[NotificationStatus.DELIVERED] > 0
                ? (analyticsData.statusCounts[NotificationStatus.READ] / analyticsData.statusCounts[NotificationStatus.DELIVERED]) * 100
                : 0;

            const failureRate = analyticsData.totalMessages > 0
                ? (analyticsData.statusCounts[NotificationStatus.FAILED] / analyticsData.totalMessages) * 100
                : 0;

            const deliveredNotifications = notifications.filter(n => n.deliveredAt && n.sentAt);
            const readNotifications = notifications.filter(n => n.readAt && n.deliveredAt);

            const averageDeliveryTime = deliveredNotifications.length > 0
                ? deliveredNotifications.reduce((sum, n) => sum + ((n.deliveredAt! - n.sentAt!) / 1000), 0) / deliveredNotifications.length
                : undefined;

            const averageReadTime = readNotifications.length > 0
                ? readNotifications.reduce((sum, n) => sum + ((n.readAt! - n.deliveredAt!) / 1000), 0) / readNotifications.length
                : undefined;

            const segmentBreakdown: CampaignAnalytics['segmentBreakdown'] = {};
            Object.entries(analyticsData.segmentCounts).forEach(([segment, count]) => {
                if (count > 0) {
                    const segmentNotifications = notifications.filter(n => n.customerSegment === segment);
                    const segmentDelivered = segmentNotifications.filter(n => n.status === NotificationStatus.DELIVERED).length;
                    const segmentRead = segmentNotifications.filter(n => n.status === NotificationStatus.READ).length;
                    
                    segmentBreakdown[segment as CustomerSegment] = {
                        count,
                        deliveryRate: (segmentDelivered / count) * 100,
                        readRate: segmentDelivered > 0 ? (segmentRead / segmentDelivered) * 100 : 0
                    };
                }
            });

            const timeSeriesMap = new Map<number, { sent: number; delivered: number; read: number; failed: number }>();
            analyticsData.timeSeriesData.forEach(point => {
                const existing = timeSeriesMap.get(point.timestamp) || { sent: 0, delivered: 0, read: 0, failed: 0 };
                switch (point.status) {
                    case NotificationStatus.SENT:
                        existing.sent += point.count;
                        break;
                    case NotificationStatus.DELIVERED:
                        existing.delivered += point.count;
                        break;
                    case NotificationStatus.READ:
                        existing.read += point.count;
                        break;
                    case NotificationStatus.FAILED:
                        existing.failed += point.count;
                        break;
                }
                timeSeriesMap.set(point.timestamp, existing);
            });

            const timeSeriesData = Array.from(timeSeriesMap.entries())
                .map(([timestamp, data]) => ({ 
                    timestamp, 
                    timestampISO: this.toISTISOString(timestamp), // 🆕 Add IST ISO timestamp
                    ...data 
                }))
                .sort((a, b) => a.timestamp - b.timestamp);

            return {
                campaignId,
                campaignName: firstNotification.campaignName || campaignId,
                campaignType: firstNotification.campaignType || CampaignType.MARKETING,
                messageCategory: firstNotification.messageCategory || MessageCategory.GENERAL,
                
                totalMessages: analyticsData.totalMessages,
                sentCount: analyticsData.statusCounts[NotificationStatus.SENT],
                deliveredCount: analyticsData.statusCounts[NotificationStatus.DELIVERED],
                readCount: analyticsData.statusCounts[NotificationStatus.READ],
                failedCount: analyticsData.statusCounts[NotificationStatus.FAILED],
                
                deliveryRate,
                readRate,
                failureRate,
                
                averageDeliveryTime,
                averageReadTime,
                
                segmentBreakdown,
                timeSeriesData,
                
                startDate: firstNotification.timestamp,
                startDateISO: this.toISTISOString(firstNotification.timestamp), // 🆕 Add IST ISO timestamp
                endDate: lastNotification.timestamp,
                endDateISO: this.toISTISOString(lastNotification.timestamp), // 🆕 Add IST ISO timestamp
                duration: Math.floor((lastNotification.timestamp - firstNotification.timestamp) / 1000)
            };

        } catch (error) {
            console.error('Error getting campaign analytics:', error);
            throw error;
        }
    }

    async getCustomerEngagement(mobileNumber: string, businessId?: string): Promise<CustomerEngagementAnalytics> {
        try {
            const filters: NotificationQueryFilters = {
                mobileNumber,
                ...(businessId && { businessId })
            };

            const notifications = await this.repository.queryWithFilters(filters);
            
            if (notifications.length === 0) {
                throw new Error(`No notifications found for customer ${mobileNumber}`);
            }

            notifications.sort((a, b) => b.timestamp - a.timestamp);
            
            const latestNotification = notifications[0];
            
            const totalDelivered = notifications.filter(n => n.status === NotificationStatus.DELIVERED).length;
            const totalRead = notifications.filter(n => n.status === NotificationStatus.READ).length;
            
            const deliveryRate = notifications.length > 0 ? (totalDelivered / notifications.length) * 100 : 0;
            const readRate = totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0;

            const campaignParticipation = notifications
                .filter(n => n.campaignId)
                .map(n => ({
                    campaignId: n.campaignId!,
                    campaignName: n.campaignName || n.campaignId!,
                    campaignType: n.campaignType || CampaignType.MARKETING,
                    messageCategory: n.messageCategory || MessageCategory.GENERAL,
                    participated: true,
                    delivered: n.status === NotificationStatus.DELIVERED || n.status === NotificationStatus.READ,
                    read: n.status === NotificationStatus.READ,
                    timestamp: n.timestamp,
                    timestampISO: this.toISTISOString(n.timestamp) // 🆕 Add IST ISO timestamp
                }));

            const categoryCount = new Map<MessageCategory, number>();
            notifications.forEach(n => {
                if (n.messageCategory) {
                    categoryCount.set(n.messageCategory, (categoryCount.get(n.messageCategory) || 0) + 1);
                }
            });
            
            const preferredMessageCategories = Array.from(categoryCount.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3)
                .map(([category]) => category);

            const deliveredNotifications = notifications.filter(n => n.deliveredAt);
            const readNotifications = notifications.filter(n => n.readAt);

            return {
                customerId: mobileNumber,
                mobileNumber,
                customerSegment: latestNotification.customerSegment || CustomerSegment.GENERAL,
                
                totalMessagesReceived: notifications.length,
                totalMessagesDelivered: totalDelivered,
                totalMessagesRead: totalRead,
                
                deliveryRate,
                readRate,
                responseRate: 0,
                
                campaignParticipation,
                preferredMessageCategories,
                
                lastMessageTimestamp: latestNotification.timestamp,
                lastMessageTimestampISO: this.toISTISOString(latestNotification.timestamp), // 🆕 Add IST ISO timestamp
                lastDeliveryTimestamp: deliveredNotifications.length > 0 ? deliveredNotifications[0].deliveredAt : undefined,
                lastDeliveryTimestampISO: deliveredNotifications.length > 0 && deliveredNotifications[0].deliveredAt 
                    ? this.toISTISOString(deliveredNotifications[0].deliveredAt) 
                    : undefined, // 🆕 Add IST ISO timestamp
                lastReadTimestamp: readNotifications.length > 0 ? readNotifications[0].readAt : undefined,
                lastReadTimestampISO: readNotifications.length > 0 && readNotifications[0].readAt 
                    ? this.toISTISOString(readNotifications[0].readAt) 
                    : undefined // 🆕 Add IST ISO timestamp
            };

        } catch (error) {
            console.error('Error getting customer engagement analytics:', error);
            throw error;
        }
    }

    async linkWithWebhookLog(notificationId: string, timestamp: number, webhookLogId: string): Promise<void> {
        await this.repository.updateLog(notificationId, timestamp, {
            webhookLogId
        });
    }

    async queryNotifications(filters: NotificationQueryFilters): Promise<NotificationLog[]> {
        return this.repository.queryWithFilters(filters);
    }

    async getNotificationsByCampaign(campaignId: string): Promise<NotificationLog[]> {
        return this.repository.getByCampaignId(campaignId);
    }

    async getNotificationsBySegment(
        segment: CustomerSegment,
        startTime?: number,
        endTime?: number
    ): Promise<NotificationLog[]> {
        return this.repository.getByCustomerSegment(segment, startTime, endTime);
    }

    async getNotificationsByCategory(
        category: MessageCategory,
        startTime?: number,
        endTime?: number
    ): Promise<NotificationLog[]> {
        return this.repository.getByMessageCategory(category, startTime, endTime);
    }
} 